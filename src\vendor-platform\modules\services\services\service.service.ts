/* eslint-disable prettier/prettier */
/* eslint-disable max-params */
/* eslint-disable prettier/prettier */
import { BucketNameEnum, uploadFileAndReturnUrl } from '@/aws/s3';
import Service from '../models/service.model';
import { HttpException } from '@vendor/exceptions/HttpExceptions';
import { Types } from 'mongoose';
import { logger } from '@/clean/lib/logger';
import StockVehicle from '@/models/StockVehicleSchema';
import { AppointmentVendor, AppointmentVendorStatus } from '../../workshop/models/appointment.model';

const mapImages = (files: Express.Multer.File[], fieldname: string) => {
  return files.filter((file) => file.fieldname.includes(fieldname)) /* .map((file) => file.path) */;
};

const categories = [
  { field: 'paintConditionImages', route: 'paintConditionImages', step: 1 } as const,
  { field: 'blowsImages', route: 'blowsImages', step: 1 } as const,
  { field: 'seatsImages', route: 'seatsImages', step: 1 } as const,
  { field: 'dashboardImages', route: 'dashboardImages', step: 1 } as const,
  { field: 'engineImages', route: 'engineImages', step: 1 } as const,
  { field: 'emergencyToolsImages', route: 'emergencyToolsImages', step: 1 } as const,
  { field: 'oilImages', route: 'oilImages', step: 2 } as const,
  { field: 'filterImages', route: 'filterImages', step: 2 } as const,
  { field: 'tuneUpImages', route: 'tuneUpImages', step: 2 } as const,
];

type FieldType = (typeof categories)[number]['field'];

export class ServiceClass {
  async createService(
    data: any,
    files: Express.Multer.File[] | { [fieldname: string]: Express.Multer.File[] } | undefined
  ) {
    // Validate if stock vehicle already has a service in progress, to avoid creating a new one

    const serviceInProgress = await Service.findOne({
      stockId: data.stockId,
      status: 'pending',
    }).select({ _id: 1 });

    if (serviceInProgress) {
      // throw new Error('There is a service in progress for this vehicle');
      throw HttpException.BadRequest('There is a service in progress for this vehicle');
    }

    data.firstStep = {
      ...data,
    };

    const id = new Types.ObjectId();
    data._id = id;
    const images = await this.uploadImages(files, data.stockId, id.toString());

    data.firstStep.bodyWorkDetail.paintConditionImages = images.paintConditionImages;

    data.firstStep.bodyWorkDetail.paintConditionImages = images.paintConditionImages;

    data.firstStep.bodyWorkDetail.blowsImages = images.blowsImages;

    data.firstStep.seatsImages = images.seatsImages;

    data.firstStep.dashboardImages = images.dashboardImages;

    data.firstStep.engineImages = images.engineImages;

    data.firstStep.emergencyToolsImages = images.emergencyToolsImages;

    data.firstStep.emergencyTools = data.emergency.emergencyTools;

    if (data.secondStep) {
      delete data.secondStep;
    }

    if (data.arrivalKm) {
      await StockVehicle.updateOne(
        { _id: data.stockId },
        {
          $set: {
            km: data.arrivalKm,
          },
        }
      );
    }

    const newService = new Service(data);
    await newService.save();
    logger.info('Service created', { serviceId: newService._id });
    await this.markAppointmentAsCompleted(data.stockId);
    return newService;
  }

  async markAppointmentAsCompleted(stockId: string) {
    const appointment = await AppointmentVendor.findOne({
      stockId,
      status: AppointmentVendorStatus.scheduled,
    }).sort({ createdAt: -1 });

    if (!appointment) {
      return null;
    }
    appointment.status = AppointmentVendorStatus.completed;
    await appointment.save();
    return appointment;
  }

  async uploadSignature(file: Express.Multer.File, stockId: string, serviceId: string) {
    const uploaded = await uploadFileAndReturnUrl(file, {
      route: `${stockId}/${serviceId}/signature`,
      isPublic: false,
      bucketName: BucketNameEnum.VENDOR_PLATFORM,
    });

    return uploaded;
  }

  async uploadImages(
    files: Express.Multer.File[] | { [fieldname: string]: Express.Multer.File[] } | undefined,
    stockId: string,
    serviceId: string,
    step: number = 1
  ) {
    const results = await Promise.all(
      categories
        .filter((category) => category.step === step)
        .map(async (category) => {
          const filesForCategory = mapImages(files as Express.Multer.File[], category.field);
          const uploaded = await Promise.all(
            filesForCategory.map((file) =>
              uploadFileAndReturnUrl(file, {
                // route: `${stockId}/${category.route}`,
                route: `${stockId}/${serviceId}/${category.route}`,
                bucketName: BucketNameEnum.VENDOR_PLATFORM,
              })
            )
          );
          return { field: category.field, urls: uploaded.map((u) => u.url) };
        })
    );

    const d = results.reduce(
      (acc, curr) => ({
        ...acc,
        [curr.field]: curr.urls,
      }),
      {} as Record<FieldType, string[]>
    );
    return d;
  }

  async findServicesByStockId(
    {
      stockId,
      associateId,
      organizationId,
      options,
    }: {
      stockId: string;
      associateId?: string;
      organizationId?: string;
      options: { includeWorkshop?: boolean };
    }
  ) {
    let queryObj: { [key: string]: any } = {
      stockId,
      // organizationId,
    };

    if (organizationId) {
      // Validate if organizationId is not undefined, because this is endpoint/service is used by admin and vendor platforms
      // and the organizationId is not present in the request for admin platform
      queryObj = {
        ...queryObj,
        organizationId,
      };
    }
    if (associateId) {
      queryObj = {
        ...queryObj,
        associateId,
      };
    }

    if (options.includeWorkshop) {
      const services = await Service.find(queryObj)
        .sort({ createdAt: -1 })
        .populate({
          path: 'workshop',
          select: {
            name: 1,
          },
        });
      return services;
    }

    const services = await Service.find(queryObj).sort({ createdAt: -1 });

    return services;
  }

  /**
   * Complete created service (Second step)
   */

  async completeService(id: string, data: any, files: Express.Multer.File[] | undefined) {
    const filterObj = {
      _id: id,
      status: 'pending',
      organizationId: data.organizationId,
    };
    const service = await Service.findOne(filterObj);

    if (!service) throw new Error('Service not found');

    const stepNumber = 2;

    const images = await this.uploadImages(files, service.stockId, service._id.toString(), stepNumber);

    if (images.oilImages) data.serviceDetail.specifications.oilImages = images.oilImages;
    if (images.filterImages) data.serviceDetail.specifications.filterImages = images.filterImages;
    if (images.tuneUpImages) data.serviceDetail.specifications.tuneUpImages = images.tuneUpImages;

    const signatureFile = files?.find((file) => file.fieldname === 'signature');
    if (signatureFile) {
      try {
        const signatureUrl = await this.uploadSignature(
          signatureFile,
          service.stockId,
          service._id.toString()
        );
        data.signature = signatureUrl.Key;
      } catch (error) {
        logger.error('Error uploading signature', error);
      }
    }

    service.secondStep = data;

    service.status = 'completed';
    service.completedAt = new Date();

    await service.save();

    logger.info('Service completed', { serviceId: service._id });
    return service;
  }

  async getOrganizationServices(
    organizationId: string,
    options: { page: number; limit: number } = { page: 0, limit: 10 }
  ) {
    let queryObj = {
      organizationId,
    };

    const totalResults = await Service.countDocuments(queryObj);
    const totalPages = Math.ceil(totalResults / options.limit);

    const skip = options.page ? Number(options.page) - 1 : 0;

    const services = await Service.find(queryObj)
      .skip(skip * Number(options.limit))
      .limit(Number(options.limit))
      .sort({ createdAt: -1 })
      .populate({
        path: 'workshop',
        select: {
          name: 1,
        },
      })
      .lean();

    const stockIds = services.map((service) => service.stockId);

    const stockVehicles = await StockVehicle.find({ _id: { $in: stockIds } })
      .select({
        carPlates: 1,
        drivers: 1,
      })
      .populate({
        path: 'associates',
        select: {
          firstName: 1,
          lastName: 1,
          email: 1,
          phone: 1,
        },
      });

    // Create a map with the stock data

    const stockMap = stockVehicles.reduce((acc, curr) => {
      acc[curr._id.toString()] = curr;
      return acc;
    }, {} as Record<string, any>);

    // Enrich the services data with the stock data

    const servicesEnriched = services.map((service) => {
      const stock = stockMap[service.stockId];
      (service as any).stock = stock;
      return service;
    });

    return {
      services: servicesEnriched,
      totalResults,
      totalPages,
    };
  }

  async findServicesByAssociateId(associateId: string, organizationId?: string) {
    const services = await Service.find({ $or: [{ organizationId, associateId }, { associateId }] }).sort({
      createdAt: -1,
    });

    return services;
  }
}

export const serviceClassInstance = new ServiceClass();
