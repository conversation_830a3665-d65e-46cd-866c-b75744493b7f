interface Permission {
  section: string;
  subSections: {
    subSection: string;
    capabilities: string[];
  }[];
}

export const PermissionMatrix: Permission[] = [
  {
    section: 'dashboard',
    subSections: [{ subSection: '', capabilities: ['view'] }],
  },
  {
    section: 'userManagement',
    subSections: [
      {
        subSection: 'users',
        capabilities: ['view', 'add', 'edit', 'makeHomeVisitor', 'delete', 'assignPermissions'],
      },
      { subSection: 'permissions', capabilities: ['view', 'edit', 'add', 'delete'] },
    ],
  },
  {
    section: 'calendar',
    subSections: [
      { subSection: 'appointmentScheduler', capabilities: ['view', 'edit'] },
      {
        subSection: 'calendarPreview',
        capabilities: ['viewMyCalendar', 'viewAllCalendar', 'editMyCalendar', 'editAllCalendar'],
      },
    ],
  },
  {
    section: 'clients',
    subSections: [
      {
        subSection: 'admissions',
        capabilities: [
          'view',
          'add',
          'editProfile',
          'uploadDocument',
          'replaceDocument',
          'approveDocument',
          'declineDocument',
          'addEarnings',
          'editEarnings',
          'addGuarantee',
          'editGuarantee',
        ],
      },
      { subSection: 'waitlist', capabilities: ['view'] },
      { subSection: 'leads', capabilities: ['view'] },
      { subSection: 'assignedLeads', capabilities: ['view', 'myTeam', 'allTeam'] },
    ],
  },
  {
    section: 'fleet',
    subSections: [
      {
        subSection: 'general',
        capabilities: [
          'add',
          'editVehicle',
          'viewDriverProfile',
          'editDriverProfile',
          'viewContractDetails',
          'addPolicy',
          'editPolicy',
          'addHolding',
          'editHolding',
          'changeStatus',
          'addPlate',
          'editPlate',
          'addCirculationCard',
          'editCirculationCard',
          'toRecovery',
          'generateContract',
          'regenarateContract',
          'returnStep',
          'sendContractToSignature',
          'assignDriver',
          'ocnSignature',
          'editGPS',
        ],
      },
      {
        subSection: 'active',
        capabilities: ['view'],
      },
      {
        subSection: 'inactive',
        capabilities: [
          'view',
          'in-preparation',
          'stock',
          'assigned',
          'delivered',
          'insurance',
          'workshop',
          'revision',
          'legal',
          'collection',
          'withdrawn',
          'sold',
          'adendum',
          'utilitary',
        ],
      },
      {
        subSection: 'qrScan',
        capabilities: ['view', 'edit'],
      },
    ],
  },
  {
    section: 'payments',
    subSections: [
      {
        subSection: 'clients',
        capabilities: ['view', 'edit', 'delete', 'requestPayment', 'createInvoice', 'createReceipt'],
      },
      {
        subSection: 'products',
        capabilities: ['view', 'add', 'edit'],
      },
      {
        subSection: 'payments',
        capabilities: ['view', 'add'],
      },
      {
        subSection: 'paymentSchedule',
        capabilities: ['view'],
      },
      {
        subSection: 'paymentVerification',
        capabilities: ['view'],
      },
    ],
  },
  {
    section: 'globalSearch',
    subSections: [{ subSection: '', capabilities: ['search'] }],
  },
];
