import jwt from 'jsonwebtoken';
import { OAuth2Client } from 'google-auth-library';
import { officeManagementAccessTokenSecret, GOOGLE_SSO_CONFIG, UserRole } from '../../../constants';
import { ValidationHelpers } from '../../../utils/validation-helpers';
import { HttpException } from '../../../exceptions/HttpExceptions';

export interface GoogleTokenPayload {
  email: string;
  name: string;
  picture: string;
  sub: string;
  email_verified: boolean;
}

export interface AuthResponse {
  token: string;
  user: {
    id: string;
    email: string;
    name: string;
    role: UserRole;
    picture?: string;
  };
}

export class AuthService {
  private googleClient: OAuth2Client;

  constructor() {
    this.googleClient = new OAuth2Client(
      GOOGLE_SSO_CONFIG.CLIENT_ID,
      GOOGLE_SSO_CONFIG.CLIENT_SECRET,
      GOOGLE_SSO_CONFIG.REDIRECT_URI
    );
  }

  /**
   * Verify Google ID token and authenticate user
   */
  async authenticateWithGoogle(idToken: string): Promise<AuthResponse> {
    try {
      // Verify the Google ID token
      const ticket = await this.googleClient.verifyIdToken({
        idToken,
        audience: GOOGLE_SSO_CONFIG.CLIENT_ID,
      });

      const payload = ticket.getPayload() as GoogleTokenPayload;
      
      if (!payload) {
        throw HttpException.Unauthorized('Invalid Google token');
      }

      // Validate email domain
      if (!ValidationHelpers.validateEmailDomain(payload.email)) {
        throw HttpException.Forbidden('Only @onecarnow email addresses are allowed');
      }

      if (!payload.email_verified) {
        throw HttpException.Forbidden('Email must be verified');
      }

      // Get or create user
      const user = await this.getOrCreateUser(payload);

      // Generate JWT token
      const token = this.generateJWTToken(user);

      return {
        token,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          picture: user.picture,
        },
      };
    } catch (error) {
      console.error('Google authentication error:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw HttpException.Unauthorized('Authentication failed');
    }
  }

  /**
   * Generate JWT token for user
   */
  private generateJWTToken(user: any): string {
    const payload = {
      userId: user.id,
      email: user.email,
      role: user.role,
    };

    return jwt.sign(payload, officeManagementAccessTokenSecret, {
      expiresIn: '24h',
    });
  }

  /**
   * Get or create user from Google payload
   */
  private async getOrCreateUser(payload: GoogleTokenPayload): Promise<any> {
    // This would typically interact with your user database
    // For now, we'll create a mock user object
    
    // Determine user role based on email or other criteria
    const role = this.determineUserRole(payload.email);

    return {
      id: payload.sub,
      email: payload.email,
      name: payload.name,
      picture: payload.picture,
      role,
      googleId: payload.sub,
      emailVerified: payload.email_verified,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  /**
   * Determine user role based on email or other criteria
   */
  private determineUserRole(email: string): UserRole {
    // This logic can be customized based on your requirements
    // For example, you might have a list of admin emails or check against a database
    
    const adminEmails = [
      '<EMAIL>',
      '<EMAIL>',
      // Add more admin emails as needed
    ];

    const receptionistEmails = [
      '<EMAIL>',
      '<EMAIL>',
      // Add more receptionist emails as needed
    ];

    if (adminEmails.includes(email)) {
      return UserRole.ADMIN;
    }

    if (receptionistEmails.includes(email)) {
      return UserRole.RECEPTIONIST;
    }

    return UserRole.EMPLOYEE;
  }

  /**
   * Verify JWT token
   */
  verifyToken(token: string): any {
    try {
      return jwt.verify(token, officeManagementAccessTokenSecret);
    } catch (error) {
      throw HttpException.Unauthorized('Invalid token');
    }
  }

  /**
   * Generate Google OAuth URL
   */
  generateGoogleAuthUrl(): string {
    const scopes = [
      'https://www.googleapis.com/auth/userinfo.email',
      'https://www.googleapis.com/auth/userinfo.profile',
    ];

    return this.googleClient.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      include_granted_scopes: true,
    });
  }

  /**
   * Exchange authorization code for tokens
   */
  async exchangeCodeForTokens(code: string): Promise<AuthResponse> {
    try {
      const { tokens } = await this.googleClient.getToken(code);
      
      if (!tokens.id_token) {
        throw HttpException.BadRequest('No ID token received');
      }

      return await this.authenticateWithGoogle(tokens.id_token);
    } catch (error) {
      console.error('Token exchange error:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw HttpException.BadRequest('Failed to exchange authorization code');
    }
  }
}
