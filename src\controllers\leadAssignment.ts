/* eslint-disable @typescript-eslint/naming-convention */
import { Request, Response } from 'express';
import { Types, Error as MongooseError, isValidObjectId } from 'mongoose';
import { ZodError, z } from 'zod';
import { AdmissionRequestMongo } from '../models/admissionRequestSchema';
import { logger } from '../clean/lib/logger';
import { BlockLeadAssignation } from '@/models/blockLeadAssignationSchema';
import { Areas, BlockLeadAssignationReason, Roles } from '@/constants';
import { UserMongo } from '@/models/userSchema';
import { Counter } from '@/models/counterSchema';
import { BlockLeadAssignationAudit } from '@/models/blockLeadAssignationAudit';
import lodash from 'lodash';

interface BlockData {
  blockedFrom?: Date;
  blockedUntil?: Date;
  reason?: string;
}

const getChangeDescription = (oldData: BlockData, newData: BlockData): string => {
  const changedFields = Object.keys(newData).filter(
    (key) => oldData[key as keyof BlockData] !== newData[key as keyof BlockData]
  );
  return changedFields
    .map((key) => {
      const oldValue = oldData[key as keyof BlockData];
      const newValue = newData[key as keyof BlockData];
      return `${key}: "${oldValue}" → "${newValue}"`;
    })
    .join(', ');
};

// Allowed fields for filtering/sorting/selection
const ALLOWED_FIELDS = [
  '_id',
  'createdAt',
  'personalData.firstName',
  'personalData.lastName',
  'status',
  'typeOfPreapproval',
  'documentsAnalysis.status',
  'personalData.phone',
  'personalData.email',
  'personalData.state',
  'personalData.city',
  'personalData.age',
  'agentId',
  'agentName', // Newly added
];

// Zod schema for query parameters
const QuerySchema = z
  .object({
    page: z.coerce.number().int().min(1).default(1),
    limit: z.coerce.number().int().min(1).max(100).default(10),
    sortField: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).default('desc'),
    fields: z.string().optional(),
    // Explicitly validate _id
    _id: z
      .string()
      .optional()
      .refine((val) => !val || Types.ObjectId.isValid(val), {
        message: 'Formato de ObjectId inválido para _id',
      }),
    agentId: z
      .string()
      .optional()
      .refine((val) => !val || Types.ObjectId.isValid(val), {
        message: 'El formato de ObjectId para agentId no es válido.',
      }),
    agentName: z.string().optional(),
  })
  .catchall(z.unknown());

// Prevent NoSQL injection by sanitizing query operators
const SAFE_OPERATORS = ['$regex', '$options', '$in', '$nin', '$ne', '$gt', '$lt', '$gte', '$lte'];

const sanitizeQuery = (query: Record<string, any>): Record<string, any> => {
  const sanitized: Record<string, any> = {};

  for (const [key, value] of Object.entries(query)) {
    // Disallow unsafe top-level keys
    if (key.startsWith('$') && !SAFE_OPERATORS.includes(key)) {
      throw new Error(`Parámetro de consulta inválido: ${key}`);
    }

    // Special handling for known ID/name fields
    if (['_id', 'agentId', 'agentName'].includes(key)) {
      if (typeof value === 'object' && !Array.isArray(value) && value !== null) {
        for (const subKey of Object.keys(value)) {
          if (subKey.startsWith('$') && !SAFE_OPERATORS.includes(subKey)) {
            throw new Error(`Operador inválido en campo especial: ${key}.${subKey}`);
          }
        }
      }
      sanitized[key] = value;
      continue;
    }

    // Sanitize arrays recursively
    if (Array.isArray(value)) {
      sanitized[key] = value.map((item) =>
        typeof item === 'object' && item !== null ? sanitizeQuery(item) : item
      );
    } else if (typeof value === 'object' && value !== null) {
      // Sanitize nested objects
      const innerSanitized: Record<string, any> = {};
      for (const [subKey, subValue] of Object.entries(value)) {
        if (subKey.startsWith('$') && !SAFE_OPERATORS.includes(subKey)) {
          throw new Error(`Operador inválido en filtro anidado: ${subKey}`);
        }
        innerSanitized[subKey] = sanitizeQuery({ dummy: subValue }).dummy;
      }
      sanitized[key] = innerSanitized;
    } else {
      sanitized[key] = value;
    }
  }

  return sanitized;
};

// Parse complex filters like age[gte]=25
const parseFilters = (filters: Record<string, any>): Record<string, any> => {
  const query: Record<string, any> = {};
  Object.entries(filters).forEach(([key, value]) => {
    if (!key.includes('[') && !ALLOWED_FIELDS.includes(key) && key !== 'agentName') return;

    // Handle agentName filter
    if (key === 'agentName') {
      query.agentName = { $regex: value as string, $options: 'i' };
      return;
    }

    // Handle nested fields with operators
    const fieldPath = key.replace(/\.$/, '');
    if (/\[.*?\]/.test(fieldPath)) {
      const match = fieldPath.match(/(.*?)$$(.*?)$$/);
      if (match) {
        const [, field, operator] = match;
        const validOperators = ['gte', 'lte', 'gt', 'lt', 'ne', 'in', 'nin'];
        if (!validOperators.includes(operator)) return;

        const fieldValue =
          operator === 'in' || operator === 'nin'
            ? value.split(',')
            : isNaN(Number(value))
              ? value
              : Number(value);

        query[field] = {
          ...query[field],
          [`$${operator}`]: fieldValue,
        };
      }
    } else if (value) {
      // Special case for ObjectId
      if (key === '_id') {
        query._id = new Types.ObjectId(value as string);
      } else if (key === 'agentId') {
        query.agentId = new Types.ObjectId(value as string);
      } else if (key === 'createdAt' && value.includes(',')) {
        const [start, end] = value.split(',');
        query.createdAt = {
          ...(start ? { $gte: new Date(start) } : {}),
          ...(end ? { $lte: new Date(end) } : {}),
        };
      } else if (
        [
          'personalData.firstName',
          'personalData.lastName',
          'personalData.phone',
          'personalData.email',
          'personalData.state',
        ].includes(key)
      ) {
        query[key] = { $regex: value as string, $options: 'i' };
      } else if (key === 'status') {
        query.status = { $in: value.split(',').map((v: string) => v.trim()) };
      } else if (key === 'personalData.city') {
        query['personalData.city'] = { $in: value.split(',').map((v: string) => v.trim()) };
      } else {
        query[key] = value;
      }
    }
  });
  return query;
};

export const getAdmissionRequests = async (req: Request, res: Response): Promise<any> => {
  try {
    // Validate query parameters with Zod
    const result = QuerySchema.safeParse(req.query);
    if (!result.success) {
      return res.status(400).json({
        error: 'La validación falló.',
        details: result.error.errors.map((e) => ({
          path: e.path.join('.'),
          message: e.message,
        })),
      });
    }

    const { page, limit, sortField, sortOrder, fields } = result.data;

    // Extract filter params including agentName
    const filterParams = Object.entries(req.query).reduce(
      (acc, [key, value]) => {
        if (ALLOWED_FIELDS.includes(key) || key === 'agentName') {
          acc[key] = value;
        }
        return acc;
      },
      {} as Record<string, any>
    );

    // Validate sort field
    if (sortField && !ALLOWED_FIELDS.includes(sortField)) {
      return res.status(400).json({
        error: 'Campo de ordenación inválido.',
        details: `Campos permitidos: ${ALLOWED_FIELDS.join(', ')}`,
      });
    }

    // Validate field selection
    if (fields) {
      const fieldList = fields.split(',');
      if (fieldList.some((f) => !ALLOWED_FIELDS.includes(f))) {
        return res.status(400).json({
          error: 'Campos solicitados inválidos.',
          details: `Campos permitidos: ${ALLOWED_FIELDS.join(', ')}`,
        });
      }
    }

    // Build query
    const rawQuery = parseFilters(filterParams);
    const query = sanitizeQuery(rawQuery);

    // Pagination
    const skip = (page - 1) * limit;

    // Aggregation pipeline to join with UserMongo
    const pipeline = [];

    pipeline.push({
      $match: {
        'personalData.country': 'mx',
      },
    });

    // Join with User collection to get agent name
    pipeline.push({
      $lookup: {
        from: 'users',
        localField: 'agentId',
        foreignField: '_id',
        as: 'agentInfo',
      },
    });

    // Extract agent name
    pipeline.push({
      $addFields: {
        agentName: { $arrayElemAt: ['$agentInfo.name', 0] },
      },
    });

    // Remove temporary field
    pipeline.push({ $project: { agentInfo: 0 } });

    // Match main filter (now agentName is available)
    if (Object.keys(query).length > 0) {
      pipeline.push({ $match: query });
    }

    // Count total matching documents
    const countPipeline = [...pipeline, { $count: 'total' }];
    const countResult = await AdmissionRequestMongo.aggregate(countPipeline).exec();
    const total = countResult.length > 0 ? countResult[0].total : 0;
    const totalPages = Math.ceil(total / limit);

    // Sorting logic
    if (sortField) {
      if (sortField === 'agentName') {
        pipeline.push({
          $sort: {
            agentName: (sortOrder === 'desc' ? -1 : 1) as 1 | -1,
          },
        });
      } else {
        pipeline.push({
          $sort: {
            [sortField]: (sortOrder === 'desc' ? -1 : 1) as 1 | -1,
          },
        });
      }
    }

    // Pagination
    pipeline.push({ $skip: skip });
    pipeline.push({ $limit: limit });

    // Final projection for field selection
    if (fields) {
      const selectedFields = fields.split(',').reduce(
        (acc, field) => {
          acc[field] = 1;
          return acc;
        },
        {} as Record<string, number>
      );
      pipeline.push({ $project: selectedFields });
    }

    // Execute aggregation
    const paginatedData = await AdmissionRequestMongo.aggregate(pipeline).exec();

    // Transform data before sending response
    const simplifiedData = paginatedData.map((doc) => ({
      _id: doc._id,
      status: doc.status,
      documentsAnalysis: {
        status: doc.documentsAnalysis?.status || null,
      },
      personalData: {
        firstName: doc.personalData?.firstName || null,
        lastName: doc.personalData?.lastName || null,
        phone: doc.personalData?.phone || null,
        email: doc.personalData?.email || null,
        city: doc.personalData?.city || null,
        state: doc.personalData?.state || null,
        age: doc.personalData?.age || null,
      },
      typeOfPreapproval: doc.typeOfPreapproval,
      agentId: doc.agentId,
      agentName: doc.agentName || null,
      createdAt: doc.createdAt,
    }));

    return res.status(200).json({
      data: simplifiedData,
      pagination: {
        total,
        page,
        limit,
        totalPages,
      },
    });
  } catch (error: any) {
    logger.error('Error fetching admission requests:', error);
    if (error instanceof MongooseError.CastError) {
      return res.status(400).json({
        error: 'Formato de ID inválido.',
        details: error.message,
      });
    }
    if (error instanceof ZodError) {
      return res.status(400).json({
        error: 'La validación falló.',
        details: error.errors.map((e) => ({
          path: e.path.join('.'),
          message: e.message,
        })),
      });
    }
    if (error.message.includes('Campos solicitados inválidos.')) {
      return res.status(400).json({
        error: 'La validación falló.d',
        details: error.message,
      });
    }
    if (error.message.includes('Parámetro de consulta inválido')) {
      return res.status(400).json({
        error: 'Consulta inválida.',
        details: error.message,
      });
    }
    return res.status(500).json({
      error: 'Internal Server Error',
      details: error.message,
    });
  }
};

export const blockLeadsAssignation = async (req: Request, res: Response): Promise<any> => {
  const { agentIds, blockedFrom, blockedUntil, reason, blockedBy, userId } = req.body;

  // Validate required fields
  if (
    !agentIds ||
    !Array.isArray(agentIds) ||
    agentIds.length === 0 ||
    !blockedFrom ||
    !blockedUntil ||
    !reason ||
    !blockedBy
  ) {
    return res.status(400).json({ message: 'Campos obligatorios faltantes.' });
  }

  // Validate block reason enum
  const validReasons = Object.values(BlockLeadAssignationReason);
  if (!validReasons.includes(reason)) {
    return res.status(400).json({ message: `Razón inválida: ${reason}` });
  }

  // Validate blockedBy is a valid user
  if (!isValidObjectId(blockedBy) || !(await UserMongo.findById(blockedBy))) {
    return res.status(400).json({ message: 'Uso inválido de blockedBy.' });
  }

  // Parse dates
  const parsedBlockedFrom = new Date(blockedFrom);
  const parsedBlockedUntil = new Date(blockedUntil);

  if (isNaN(parsedBlockedFrom.getTime()) || isNaN(parsedBlockedUntil.getTime())) {
    return res.status(400).json({ message: 'Formato de fecha inválido.' });
  }

  if (parsedBlockedUntil <= parsedBlockedFrom) {
    return res.status(400).json({ message: 'Bloqueado hasta debe ser posterior a bloqueado desde.' });
  }

  try {
    const overlappingAgentInfo: any[] = [];

    for (const agentId of agentIds) {
      if (!isValidObjectId(agentId)) {
        overlappingAgentInfo.push({
          agentId,
          error: 'ID de agente inválido.',
        });
        continue;
      }

      const agent = await UserMongo.findById(agentId);
      if (!agent) {
        overlappingAgentInfo.push({
          agentId,
          error: 'Agente no encontrado.',
        });
        continue;
      }

      // Check for overlapping blocks
      const overlappingBlocks = await BlockLeadAssignation.find({
        agent: agentId,
        blockedFrom: { $lt: parsedBlockedUntil },
        blockedUntil: { $gt: parsedBlockedFrom },
      });

      if (overlappingBlocks.length > 0) {
        overlappingAgentInfo.push({
          agentId,
          agentName: `${agent.name}`, // adjust based on your schema
          overlappingBlocks: overlappingBlocks.map((b) => ({
            blockId: b._id.toString(),
            serialNumber: b.serialNumber,
            blockedFrom: b.blockedFrom,
            blockedUntil: b.blockedUntil,
            reason: b.reason,
            blockedBy: b.blockedBy,
          })),
        });
      }
    }

    // If any agent has overlapping blocks, reject the entire request
    if (overlappingAgentInfo.length > 0) {
      return res.status(409).json({
        message: 'Algunos agentes ya tienen bloqueos superpuestos.',
        overlappingBlocks: overlappingAgentInfo,
      });
    }

    // Fetch or create the counter, and increment it by number of agents
    const counter = await Counter.findByIdAndUpdate(
      { _id: 'blockLeadAssignationId' },
      { $inc: { seq: agentIds.length } },
      { new: true, upsert: true, setDefaultsOnInsert: true }
    );

    if (!counter) {
      throw new Error('Counter not found or could not be created');
    }

    // Generate serial numbers sequentially starting from current counter value
    const startSerial = counter.seq - agentIds.length + 1;

    const blocksToCreate = agentIds.map((agentId, idx) => ({
      agent: agentId,
      blockedFrom: parsedBlockedFrom,
      blockedUntil: parsedBlockedUntil,
      reason,
      blockedBy,
      serialNumber: startSerial + idx, // e.g., 100, 101, 102...
    }));

    const result = await BlockLeadAssignation.insertMany(blocksToCreate);

    const user = await UserMongo.findById(userId);
    if (user) {
      result.map(async (block) => {
        const agent = await UserMongo.findById(block.agent);
        await BlockLeadAssignationAudit.create({
          timestamp: new Date(),
          action: 'create',
          user: user._id,
          userName: user.name,
          agent: block.agent,
          agentName: agent?.name,
          serialNumber: block.serialNumber,
          description: `${user.name} blocked agent ${agent?.name} from ${block.blockedFrom} to ${block.blockedUntil} for reason "${block.reason} having serial number ${block.serialNumber}`,
          newData: {
            blockedFrom: parsedBlockedFrom,
            blockedUntil: parsedBlockedUntil,
            reason,
          },
        });
      });
    }

    const responsePayload = result.map((block) => ({
      ...block.toObject(),
    }));

    return res.status(201).json({
      message: `${result.length} agentes bloqueados con éxito..`,
      data: responsePayload,
    });
  } catch (error) {
    logger.error('Error blocking agents:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

// Helper function to build regex queries for name/email
const buildUserSearchQuery = (searchTerm: string) => {
  const isId = isValidObjectId(searchTerm);
  if (isId) {
    return { _id: searchTerm };
  }

  // Search by name or email using regex
  return {
    $or: [{ name: { $regex: searchTerm, $options: 'i' } }, { email: { $regex: searchTerm, $options: 'i' } }],
  };
};

export const getBlockLeadAssignation = async (req: Request, res: Response): Promise<any> => {
  const {
    page = 1,
    limit = 10,
    searchAgent,
    searchBlockedBy,
    reason,
    blockedFrom,
    blockedUntil,
    sort = '-createdAt',
    serialNumber, // 👈 Add this new query param
  } = req.query as {
    page?: string;
    limit?: string;
    searchAgent?: string;
    searchBlockedBy?: string;
    reason?: string;
    blockedFrom?: string;
    blockedUntil?: string;
    sort?: string;
    serialNumber?: string; // 👈 New filter field
  };

  const query: any = {};

  // Filter by agent (name/email/_id)
  if (searchAgent) {
    const agentSearchQuery = buildUserSearchQuery(searchAgent);
    const agentIds = await UserMongo.find(agentSearchQuery).distinct('_id');
    if (agentIds.length === 0) {
      return res.json({
        data: [],
        pagination: { total: 0, page: +page, limit: +limit, totalPages: 0 },
      });
    }
    query.agent = { $in: agentIds };
  }

  // Filter by blockedBy (name/email/_id)
  if (searchBlockedBy) {
    const blockedBySearchQuery = buildUserSearchQuery(searchBlockedBy);
    const blockedByIds = await UserMongo.find(blockedBySearchQuery).distinct('_id');
    if (blockedByIds.length === 0) {
      return res.json({
        data: [],
        pagination: { total: 0, page: +page, limit: +limit, totalPages: 0 },
      });
    }
    query.blockedBy = { $in: blockedByIds };
  }

  // Filter by reason
  if (reason) {
    query.reason = reason;
  }

  // Date range filter for blockedFrom
  if (blockedFrom || blockedUntil) {
    query.blockedFrom = {};
    if (blockedFrom) {
      query.blockedFrom.$gte = new Date(blockedFrom);
    }
    if (blockedUntil) {
      query.blockedFrom.$lte = new Date(blockedUntil);
    }
  }

  // ✅ NEW: Filter by serialNumber (exact match)
  if (serialNumber !== undefined && !isNaN(Number(serialNumber))) {
    query.serialNumber = Number(serialNumber);
  }

  // Sorting
  let sortOptions: Record<string, 1 | -1> = {};
  if (typeof sort === 'string') {
    const sortFields = sort.split(',');
    sortFields.forEach((field) => {
      const direction = field.startsWith('-') ? -1 : 1;
      const key = field.replace(/^-/, '');
      sortOptions[key] = direction;
    });
  }

  try {
    const skip = (+page - 1) * +limit;

    const [blocks, total] = await Promise.all([
      BlockLeadAssignation.find(query)
        .populate('agent', 'name email')
        .populate('blockedBy', 'name email')
        .skip(skip)
        .limit(+limit)
        .sort(sortOptions),
      BlockLeadAssignation.countDocuments(query),
    ]);

    return res.json({
      data: blocks,
      pagination: {
        total,
        page: +page,
        limit: +limit,
        totalPages: Math.ceil(total / +limit),
      },
    });
  } catch (error) {
    logger.error('Error fetching block leads:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

export const deleteBlockLeadAssignment = async (req: Request, res: Response): Promise<any> => {
  const { id, userId } = req.params;

  // Validate ObjectId format
  if (!isValidObjectId(id)) {
    return res.status(400).json({ error: 'ID de registro de bloqueo inválido.' });
  }

  try {
    const deletedRecord = await BlockLeadAssignation.findById(id);
    const result = await BlockLeadAssignation.findByIdAndDelete(id);

    if (!result) {
      return res.status(404).json({ error: 'Registro de bloqueo no encontrado.' });
    }
    const user = await UserMongo.findById(userId);
    if (user && deletedRecord) {
      const agent = await UserMongo.findById(deletedRecord.agent);
      await BlockLeadAssignationAudit.create({
        timestamp: new Date(),
        action: 'delete',
        user: user._id,
        userName: user.name,
        agent: deletedRecord.agent,
        agentName: agent?.name,
        serialNumber: deletedRecord.serialNumber,
        description: `${user.name} deleted block for agent ${agent?.name} from ${deletedRecord.blockedFrom} to ${deletedRecord.blockedUntil} for reason "${deletedRecord.reason} having serial number ${deletedRecord.serialNumber}`,
        previousData: {
          blockedFrom: deletedRecord?.blockedFrom,
          blockedUntil: deletedRecord?.blockedUntil,
          reason: deletedRecord.reason,
        },
      });
    }

    return res.json({ message: 'Bloqueo eliminado con éxito.' });
  } catch (error) {
    logger.error('Error deleting block record:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

export const updateBlockLeadAssignation = async (req: Request, res: Response): Promise<any> => {
  const { blockId, blockedFrom, blockedUntil, reason, userId } = req.body;

  // Validate required fields
  if (!blockId || !blockedFrom || !blockedUntil) {
    return res.status(400).json({ message: 'Campos obligatorios faltantes.' });
  }

  // Validate ObjectId
  if (!isValidObjectId(blockId)) {
    return res.status(400).json({ message: 'ID de bloqueo inválido.' });
  }

  // Parse dates
  const parsedBlockedFrom = new Date(blockedFrom);
  const parsedBlockedUntil = new Date(blockedUntil);

  if (isNaN(parsedBlockedFrom.getTime()) || isNaN(parsedBlockedUntil.getTime())) {
    return res.status(400).json({ message: 'Formato de fecha inválido.' });
  }

  if (parsedBlockedUntil <= parsedBlockedFrom) {
    return res.status(400).json({ message: 'Bloqueado hasta debe ser posterior a bloqueado desde.' });
  }

  // Optional: validate reason if provided
  if (reason && !Object.values(BlockLeadAssignationReason).includes(reason)) {
    return res.status(400).json({ message: `Razón inválida: ${reason}` });
  }

  try {
    // Fetch the block to update
    const blockToUpdate = await BlockLeadAssignation.findById(blockId);
    const oldBlock = lodash.cloneDeep(blockToUpdate);
    if (!blockToUpdate) {
      return res.status(404).json({ message: 'Bloqueo no encontrado.' });
    }

    const agentId = blockToUpdate.agent;

    // Check for overlapping blocks excluding current block
    const overlappingBlocks = await BlockLeadAssignation.find({
      agent: agentId,
      _id: { $ne: blockId }, // exclude current block
      blockedFrom: { $lt: parsedBlockedUntil },
      blockedUntil: { $gt: parsedBlockedFrom },
    });

    if (overlappingBlocks.length > 0) {
      const agent = await UserMongo.findById(agentId);

      return res.status(409).json({
        message: 'This update causes overlapping blocks.',
        overlappingBlocks: [
          {
            agentId,
            agentName: agent?.name || '',
            overlappingBlocks: overlappingBlocks.map((b) => ({
              blockId: b._id.toString(),
              serialNumber: b.serialNumber,
              blockedFrom: b.blockedFrom,
              blockedUntil: b.blockedUntil,
              reason: b.reason,
              blockedBy: b.blockedBy,
            })),
          },
        ],
      });
    }

    // Apply updates
    blockToUpdate.blockedFrom = parsedBlockedFrom;
    blockToUpdate.blockedUntil = parsedBlockedUntil;
    if (reason) blockToUpdate.reason = reason;

    await blockToUpdate.save();

    const user = await UserMongo.findById(userId);
    if (user && oldBlock) {
      const agent = await UserMongo.findById(oldBlock.agent);
      await BlockLeadAssignationAudit.create({
        timestamp: new Date(),
        action: 'update',
        user: user._id,
        userName: user.name,
        agent: oldBlock.agent,
        agentName: agent?.name,
        serialNumber: oldBlock.serialNumber,
        description: `${user.name} updated block for agent ${agent?.name} ${getChangeDescription({ blockedFrom: oldBlock.blockedFrom, blockedUntil: oldBlock.blockedUntil, reason: oldBlock.reason }, { blockedFrom: blockToUpdate.blockedFrom, blockedUntil: blockToUpdate.blockedUntil, reason: blockToUpdate.reason })}  having serial number ${oldBlock.serialNumber}`,
        previousData: {
          blockedFrom: oldBlock?.blockedFrom,
          blockedUntil: oldBlock?.blockedUntil,
          reason: oldBlock.reason,
        },
        newData: {
          blockedFrom: blockToUpdate?.blockedFrom,
          blockedUntil: blockToUpdate?.blockedUntil,
          reason: blockToUpdate.reason,
        },
      });
    }

    return res.status(200).json({
      message: 'Bloqueo actualizado con éxito.',
      data: blockToUpdate.toObject(),
    });
  } catch (error) {
    logger.error('Error updating block:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

// this is a temporary end point to add serial number to all old block lead assignation records
export const updateSerialNumberBlockLeadAssignment = async (req: Request, res: Response): Promise<any> => {
  // Get all documents sorted by creation date (oldest first)
  const docs = await BlockLeadAssignation.find().sort({ createdAt: 1 }).exec();

  let serialNumber = 1;
  for (const doc of docs) {
    doc.serialNumber = serialNumber++;
    await doc.save();
  }

  // Update the counter to the max serial number
  const maxSerial = docs.length > 0 ? docs[docs.length - 1].serialNumber : 0;
  Counter.updateOne({ _id: 'blockLeadAssignationId' }, { $set: { seq: maxSerial } }, { upsert: true });
  return res.status(200);
};

// Zod schema for validating request query
const LogsQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  sortField: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  fields: z.string().optional(),
  // Filters
  agentId: z.string().optional(),
  userId: z.string().optional(),
  userName: z.string().optional(),
  agentName: z.string().optional(),
  action: z.enum(['create', 'update', 'delete']).optional(),
  serialNumber: z.coerce.number().optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
});

export const getBlockLeadAssignationAuditLogs = async (req: Request, res: Response): Promise<any> => {
  try {
    const result = LogsQuerySchema.safeParse(req.query);

    if (!result.success) {
      return res.status(400).json({
        error: 'Invalid query parameters',
        details: result.error.errors.map((e) => ({
          field: e.path.join('.'),
          message: e.message,
        })),
      });
    }

    const {
      page,
      limit,
      sortField,
      sortOrder,
      fields,
      agentId,
      userId,
      userName,
      agentName,
      action,
      serialNumber,
      startDate,
      endDate,
    } = result.data;

    // Build MongoDB query
    const query: any = {};

    if (agentId && isValidObjectId(agentId)) {
      query.agent = new Types.ObjectId(agentId);
    }

    if (userId && isValidObjectId(userId)) {
      query.user = new Types.ObjectId(userId);
    }

    if (userName) {
      query.userName = userName;
    }

    if (agentName) {
      query.agentName = agentName;
    }

    if (action) {
      query.action = action;
    }

    if (typeof serialNumber === 'number') {
      query.serialNumber = serialNumber;
    }

    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) query.timestamp.$gte = new Date(startDate);
      if (endDate) query.timestamp.$lte = new Date(endDate);
    }

    // Validate sort field
    const allowedSortFields = ['timestamp', 'serialNumber', 'userName'];
    if (sortField && !allowedSortFields.includes(sortField)) {
      return res.status(400).json({
        error: 'Invalid sort field',
        allowedFields: allowedSortFields,
      });
    }

    // Build projection
    let projection: Record<string, number> = {};
    if (fields) {
      const requestedFields = fields.split(',');
      requestedFields.forEach((field) => {
        projection[field] = 1;
      });
      projection._id = 1; // Always include ID
    } else {
      projection = { __v: 0 }; // Exclude versionKey
    }

    // Pagination
    const skip = (page - 1) * limit;

    // Sort options
    const sort: Record<string, 1 | -1> = {};
    if (sortField) {
      sort[sortField] = sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.timestamp = -1; // Default sorting
    }

    // Fetch logs
    const [logs, total] = await Promise.all([
      BlockLeadAssignationAudit.find(query, projection).sort(sort).skip(skip).limit(limit),
      BlockLeadAssignationAudit.countDocuments(query),
    ]);

    const totalPages = Math.ceil(total / limit);

    return res.status(200).json({
      data: logs,
      pagination: {
        total,
        page,
        limit,
        totalPages,
      },
    });
  } catch (error: any) {
    console.error('Error fetching block lead assignation audit logs:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

export const reassignAdmissionRequest = async (req: Request, res: Response) => {
  try {
    const { requestIds, agentIds, reason, userId } = req.body;

    // Validate requestIds
    if (!requestIds || !Array.isArray(requestIds) || requestIds.length === 0) {
      logger.error('RequestIds must be a non-empty array');
      return res.status(400).json({
        success: false,
        message: 'RequestIds must be a non-empty array',
      });
    }

    // Validate block reason enum
    const validUserId = Types.ObjectId.isValid(userId);
    if (!validUserId) {
      return res.status(400).json({ message: `User Id is incorrect ${userId}` });
    }

    // Validate block reason enum
    const validReasons = Object.values(BlockLeadAssignationReason);
    if (!validReasons.includes(reason)) {
      return res.status(400).json({ message: `Razón inválida: ${reason}` });
    }

    // Validate agentIds
    if (!agentIds || !Array.isArray(agentIds) || agentIds.length === 0) {
      logger.error('AgentIds must be a non-empty array');
      return res.status(400).json({
        success: false,
        message: 'AgentIds must be a non-empty array',
      });
    }

    // Validate all agentIds are valid ObjectIds
    if (!agentIds.every((id) => Types.ObjectId.isValid(id))) {
      logger.error('All agentIds must be valid ObjectIds');
      return res.status(400).json({
        success: false,
        message: 'All agentIds must be valid ObjectIds',
      });
    }

    // Convert string IDs to ObjectId
    const objectIds = requestIds.map((id) => new Types.ObjectId(id));
    const agentObjectIds = agentIds.map((id) => new Types.ObjectId(id));

    // Check if agent is currently available
    // Find all blocked agents at this moment
    const now = new Date();
    const blockedAgents = await BlockLeadAssignation.find({
      agent: { $in: agentIds.map((a) => a._id) },
      blockedFrom: { $lte: now },
      blockedUntil: { $gte: now },
    }).distinct('agent');

    if (blockedAgents && blockedAgents.length > 0) {
      logger.error('Please reselect agents. Some of the agents are blocked', blockedAgents);
      return res.status(400).json({
        success: false,
        message: 'Please reselect agents. Some of the agents are blocked',
        data: blockedAgents,
      });
    }

    // Prepare update operations
    const updates = objectIds.map((requestId, index) => {
      const agentIndex = index % agentObjectIds.length; // round-robin
      const selectedAgentId = agentObjectIds[agentIndex];

      return {
        updateOne: {
          filter: { _id: requestId },
          update: { $set: { agentId: selectedAgentId } },
        },
      };
    });

    // Perform bulk update
    const result = await AdmissionRequestMongo.bulkWrite(updates);

    return res.json({
      success: true,
      message: `Successfully reassigned ${result.modifiedCount} admission requests using round-robin agent assignment`,
      modifiedCount: result.modifiedCount,
    });
  } catch (error) {
    logger.error('Error updating admission requests:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

export const getAvailableAgents = async (req: Request, res: Response) => {
  try {
    const agents = await UserMongo.find({
      role: Roles.agent,
      area: Areas.sales,
      isVerified: true,
    }).exec();

    if (agents.length === 0) {
      logger.error('No verified agents available for assignment');
      return res.status(200).json({
        success: true,
        data: [],
        message: 'No verified agents available.',
      });
    }

    const now = new Date();

    // Step 2: Find all blocked agents at this moment
    const blockedAgents = await BlockLeadAssignation.find({
      agent: { $in: agents.map((a) => a._id) },
      blockedFrom: { $lte: now },
      blockedUntil: { $gte: now },
    }).distinct('agent');

    // Step 3: Filter out blocked agents
    const availableAgents = agents.filter((agent) => !blockedAgents.some((id) => id.equals(agent._id)));

    if (availableAgents.length === 0) {
      return res.status(200).json({
        success: true,
        data: [],
        message: 'No agents available as all are blocked',
      });
    }

    return res.status(200).json({
      success: true,
      data: availableAgents,
      message: 'List of verified agents available for lead assignment',
    });
  } catch (error) {
    logger.error('Error fetching available agents:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

export const getAgents = async (req: Request, res: Response) => {
  try {
    const agents = await UserMongo.find(
      {
        role: Roles.agent,
        area: Areas.sales,
        isVerified: true,
      },
      '_id name email city isVerified role area'
    ).exec();

    if (agents.length === 0) {
      logger.error('No verified agents available for assignment');
      return res.status(200).json({
        success: true,
        data: [],
        message: 'No verified agents available.',
      });
    }

    return res.status(200).json({
      success: true,
      data: agents,
      message: 'List of verified agents available for lead assignment',
    });
  } catch (error) {
    logger.error('Error fetching available agents:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};
