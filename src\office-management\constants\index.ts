// Office Management Platform Constants

export const officeManagementAccessTokenSecret =
  process.env.OFFICE_MANAGEMENT_ACCESS_TOKEN_SECRET || 'office-management-secret';

// Booking types
export enum BookingType {
  MEETING_ROOM = 'meeting_room',
  DESK = 'desk',
}

// Booking status
export enum BookingStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
}

// Guest status
export enum GuestStatus {
  INVITED = 'invited',
  CONFIRMED = 'confirmed',
  CHECKED_IN = 'checked_in',
  CHECKED_OUT = 'checked_out',
  NO_SHOW = 'no_show',
}

// User roles
export enum UserRole {
  ADMIN = 'admin',
  EMPLOYEE = 'employee',
  RECEPTIONIST = 'receptionist',
}

// Time constants
export const BOOKING_DURATION_HOURS = {
  MIN: 1,
  MAX: 8,
};

export const ADVANCE_BOOKING_DAYS = {
  MIN: 0, // Same day
  MAX: 30, // 30 days in advance
};

// Notification types
export enum NotificationType {
  SLACK = 'slack',
  EMAIL = 'email',
  WHATSAPP = 'whatsapp',
}

// Error messages
export const officeManagementMessages = {
  errors: {
    unauthorized: 'Unauthorized access to office management platform',
    invalidDomain: 'Only @onecarnow email addresses are allowed',
    roomNotAvailable: 'Meeting room is not available at the requested time',
    deskNotAvailable: 'Desk is not available for the requested date',
    bookingNotFound: 'Booking not found',
    guestNotFound: 'Guest not found',
    invalidTimeSlot: 'Invalid time slot for booking',
    maxBookingsReached: 'Maximum number of bookings reached for this user',
    pastDateBooking: 'Cannot book for past dates',
    weekendBooking: 'Bookings are not allowed on weekends',
    outsideBusinessHours: 'Bookings are only allowed during business hours',
  },
  success: {
    bookingCreated: 'Booking created successfully',
    bookingCancelled: 'Booking cancelled successfully',
    guestInvited: 'Guest invitation sent successfully',
    guestCheckedIn: 'Guest checked in successfully',
    guestCheckedOut: 'Guest checked out successfully',
  },
};

// Business hours
export const BUSINESS_HOURS = {
  START: '08:00',
  END: '18:00',
};

// Slack configuration
export const SLACK_CONFIG = {
  WEBHOOK_URL: process.env.SLACK_WEBHOOK_URL,
  CHANNEL: process.env.SLACK_OFFICE_CHANNEL || '#office-bookings',
  BOT_NAME: 'Office Management Bot',
};

// WhatsApp configuration
export const WHATSAPP_CONFIG = {
  API_URL: process.env.WHATSAPP_API_URL,
  API_KEY: process.env.WHATSAPP_API_KEY,
  PHONE_NUMBER: process.env.WHATSAPP_PHONE_NUMBER,
};

// Email configuration
export const EMAIL_CONFIG = {
  FROM_EMAIL: process.env.OFFICE_MANAGEMENT_FROM_EMAIL || '<EMAIL>',
  FROM_NAME: 'OneCarNow Office Management',
};

// Google SSO configuration
export const GOOGLE_SSO_CONFIG = {
  CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
  CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET,
  REDIRECT_URI: process.env.GOOGLE_REDIRECT_URI,
  ALLOWED_DOMAIN: 'onecarnow.com',
};
