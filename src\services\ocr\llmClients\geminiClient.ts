import { logger } from '@/clean/lib/logger';
// Assuming GEMINI_FLASH_MODEL is defined in constants, e.g., 'gemini-1.5-flash-latest' or similar
import { GEMINI_MAX_OUTPUT_TOKENS, GEMINI_MODEL, GEMINI_BACKOFF_MODEL } from '@/constants';
import { GoogleGenerativeAI, GenerativeModel, GenerationConfig, Schema } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');
// Use 'const' for the main exported client, it won't be reassigned globally
export const geminiClient = genAI.getGenerativeModel({ model: GEMINI_MODEL });

// Define allowed MIME types - Add PDF support
const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf'] as const;
export type AllowedMimeType = (typeof allowedMimeTypes)[number]; // "image/jpeg" | "image/png" | "image/gif" | "image/webp" | "application/pdf"

export type Source = {
  data: string;
  media_type: AllowedMimeType;
  prompt?: string;
  filename?: string;
};

// Define StructuredSource type for the new function
// The custom JSONSchema interface is removed, using Schema from @google/generative-ai directly
export type StructuredSource = Source & {
  responseSchema: Schema; // Use Schema from the library
};

// Helper function to determine MIME type from filename
export const getMimeTypeFromFilename = (filename: string): AllowedMimeType => {
  if (!filename) {
    throw new Error('Filename is required to determine MIME type');
  }

  // Extract extension (handle files with multiple dots correctly)
  const extension = filename.split('.').pop()?.toLowerCase();

  if (!extension) {
    throw new Error('Unable to determine file extension');
  }

  // Map extensions to MIME types - Add PDF case
  switch (extension) {
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'gif':
      return 'image/gif';
    case 'webp':
      return 'image/webp';
    case 'pdf':
      return 'application/pdf';
    default:
      throw new Error(
        `Unsupported file extension: ${extension}. Supported types are jpg, jpeg, png, gif, webp, and pdf`
      );
  }
};

/**
 * Helper function to retry operations that may fail with temporary errors.
 * It handles switching to a backoff model for retries without affecting the global client.
 * @param operationFactory A function that takes a GenerativeModel client and returns the Promise to execute.
 * @param maxRetries Number of retry attempts to make
 * @returns Result of the operation
 * @throws Error if all retry attempts fail
 */
const withRetry = async <T>(
  operationFactory: (client: GenerativeModel) => Promise<T>, // Expect a factory function
  maxRetries = 1
): Promise<T> => {
  let lastError: any;
  let currentClient = geminiClient; // Start with the default client instance
  let currentModelName = GEMINI_MODEL; // Track model name for logging

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      if (attempt > 0) {
        logger.info(`[Gemini] Retry attempt ${attempt}/${maxRetries} using model ${currentModelName}`);
      }
      // Execute the operation using the current client instance for this attempt
      return await operationFactory(currentClient);
    } catch (error: any) {
      lastError = error;

      // Only retry on gemini server errors
      if (error?.status >= 500) {
        if (attempt < maxRetries) {
          const delay = Math.min(100 * Math.pow(2, attempt), 1000);
          logger.warn(`[Gemini] Service error. Will retry after ${delay}ms`);

          // Check if we need to switch to the backoff model for the *next* attempt
          // Only create the backoff client if we aren't already using it
          if (currentModelName !== GEMINI_BACKOFF_MODEL) {
            logger.warn(`[Gemini] Switching to model ${GEMINI_BACKOFF_MODEL} for next retry.`);
            // Create a *new* client instance specifically for the backoff attempt(s)
            // This does NOT modify the exported geminiClient
            currentClient = genAI.getGenerativeModel({ model: GEMINI_BACKOFF_MODEL });
            currentModelName = GEMINI_BACKOFF_MODEL;
          } else {
            logger.warn(`[Gemini] Already using backoff model ${currentModelName}. Retrying...`);
            // Continue using the same backoff client instance if already switched
          }

          await new Promise((resolve) => setTimeout(resolve, delay));
          continue; // Go to the next iteration for the retry attempt
        } else {
          logger.error(
            `[Gemini] Service error persisted after ${maxRetries} retries with model ${currentModelName}. Giving up.`
          );
        }
      } else {
        // For other types of errors, don't retry
        logger.error(`[Gemini] Encountered non-retriable error: ${error?.message || JSON.stringify(error)}`);
        break; // Exit the retry loop immediately
      }
    }
  }

  // Log the last error encountered
  logger.error(
    `[Gemini] Final error after ${maxRetries} retries: ${lastError?.message || JSON.stringify(lastError)}`
  );
  // If we reached here, it means all retries failed
  throw lastError;
};

/**
 * Process a single image/source with LLM
 */
export const parseTextFromSource = async (param: Source) => {
  logger.info(`[parseTextFromSource][Gemini] - Processing document`);

  // Determine media type if not provided
  let mediaType = param.media_type;

  if (!mediaType && param.filename) {
    mediaType = getMimeTypeFromFilename(param.filename);
    logger.info(
      `[parseTextFromSource][Gemini] - Detected MIME type ${mediaType} from filename ${param.filename}`
    );
  } else if (!mediaType) {
    throw new Error('Media type is required but was not provided and no filename was available');
  }

  // Validate MIME type
  if (!allowedMimeTypes.includes(mediaType)) {
    throw new Error(`Unsupported MIME type: ${mediaType}`);
  }

  // Pass an async function (the operation factory) to withRetry.
  // This function receives the client instance to use for the attempt.
  return withRetry(async (client) => {
    // client is provided by withRetry
    try {
      // Create parts array for the request
      const parts = [
        { text: param.prompt as string },
        {
          inlineData: {
            mimeType: mediaType,
            data: param.data,
          },
        },
      ];

      // Configure generation parameters
      const generationConfig: GenerationConfig = {
        temperature: 0,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: GEMINI_MAX_OUTPUT_TOKENS,
      };

      // Send request to Gemini API using the client passed by withRetry
      const response = await client.generateContent({
        contents: [{ role: 'user', parts }],
        generationConfig,
      });

      logger.info(`[parseTextFromSource][Gemini] - Response received`);

      // Get the response text
      const responseText = response.response.text();

      // Validate response
      if (!responseText) {
        throw new Error('Empty response from Gemini API');
      }

      // Extract JSON from response text
      const startIdx = responseText.indexOf('{');
      const endIdx = responseText.lastIndexOf('}');

      if (startIdx === -1 || endIdx === -1) {
        logger.error(`[parseTextFromSource][Gemini] - Invalid JSON response: ${responseText}`);
        throw new Error('Could not find JSON markers in response');
      }

      const jsonStr = responseText.slice(startIdx, endIdx + 1);

      // Parse the JSON
      try {
        return JSON.parse(jsonStr);
      } catch (parseError) {
        logger.error(`[parseTextFromSource][Gemini] - JSON parse error: ${parseError}`);
        throw new Error(`Failed to parse JSON response: ${parseError}`);
      }
    } catch (error) {
      logger.error(`[parseTextFromSource][Gemini] - Error during operation: ${JSON.stringify(error)}`);
      throw error; // Re-throw for the withRetry function to handle
    }
  });
};

/**
 * Process a single source (e.g., image, PDF) with LLM and get structured JSON output.
 */
export const parseStructuredTextFromSource = async (param: StructuredSource) => {
  logger.info(`[parseStructuredTextFromSource][Gemini] - Processing document for structured output`);

  // Determine media type if not provided
  let mediaType = param.media_type;

  if (!mediaType && param.filename) {
    mediaType = getMimeTypeFromFilename(param.filename);
    logger.info(
      `[parseStructuredTextFromSource][Gemini] - Detected MIME type ${mediaType} from filename ${param.filename}`
    );
  } else if (!mediaType) {
    throw new Error('Media type is required but was not provided and no filename was available');
  }

  // Validate MIME type
  if (!allowedMimeTypes.includes(mediaType)) {
    throw new Error(`Unsupported MIME type: ${mediaType}`);
  }

  // Pass an async function (the operation factory) to withRetry.
  // This function receives the client instance to use for the attempt.
  return withRetry(async (client) => {
    // client is provided by withRetry
    try {
      // Create parts array for the request
      // The prompt should NOT contain the schema definition when using responseSchema.
      const parts = [
        { text: param.prompt as string },
        {
          inlineData: {
            mimeType: mediaType,
            data: param.data,
          },
        },
      ];

      // Configure generation parameters for structured output
      const generationConfig: GenerationConfig = {
        temperature: 0,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: GEMINI_MAX_OUTPUT_TOKENS,
        responseMimeType: 'application/json', // Specify JSON output
        responseSchema: param.responseSchema, // Provide the schema
      };

      // Send request to Gemini API using the client passed by withRetry
      const response = await client.generateContent({
        contents: [{ role: 'user', parts }],
        generationConfig,
      });

      logger.info(`[parseStructuredTextFromSource][Gemini] - Response received`);

      // Get the response text
      const responseText = response.response.text();

      // Validate response
      if (!responseText) {
        throw new Error('Empty response from Gemini API');
      }

      // Parse the JSON (Gemini should return valid JSON as per the schema)
      try {
        return JSON.parse(responseText);
      } catch (parseError) {
        logger.error(
          `[parseStructuredTextFromSource][Gemini] - JSON parse error: ${parseError}. Response: ${responseText}`
        );
        throw new Error(`Failed to parse JSON response: ${parseError}`);
      }
    } catch (error) {
      logger.error(
        `[parseStructuredTextFromSource][Gemini] - Error during operation: ${JSON.stringify(error)}`
      );
      throw error; // Re-throw for the withRetry function to handle
    }
  });
};

/**
 * Process multiple images with LLM
 * Used for comparisons like selfie vs INE photo
 */
// Define the part types for Gemini API
type TextPart = {
  text: string;
};

type InlineDataPart = {
  inlineData: {
    mimeType: AllowedMimeType;
    data: string;
  };
};

type Part = TextPart | InlineDataPart;

export const parseTextFromMultipleSources = async (sources: Source[], prompt: string) => {
  logger.info(`[parseMultipleImages][Gemini] - Processing multiple images`);

  // Pass an async function (the operation factory) to withRetry.
  return withRetry(async (client) => {
    // client is provided by withRetry
    try {
      // Create the parts array with text prompt first
      const parts: Part[] = [{ text: prompt }];

      // Add all images/sources to parts
      for (const source of sources) {
        // Determine media type if not provided
        let mediaType = source.media_type;

        if (!mediaType && source.filename) {
          mediaType = getMimeTypeFromFilename(source.filename);
          logger.info(
            `[parseMultipleImages][Gemini] - Detected MIME type ${mediaType} from filename ${source.filename}`
          );
        } else if (!mediaType) {
          throw new Error('Media type is required but was not provided and no filename was available');
        }

        // Validate MIME type
        if (!allowedMimeTypes.includes(mediaType)) {
          throw new Error(`Unsupported MIME type: ${mediaType}`);
        }

        // Add image to parts
        parts.push({
          inlineData: {
            mimeType: mediaType,
            data: source.data,
          },
        });
      }

      // Configure generation parameters
      const generationConfig: GenerationConfig = {
        temperature: 0,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: GEMINI_MAX_OUTPUT_TOKENS,
      };

      // Send request to Gemini API using the client passed by withRetry
      const response = await client.generateContent({
        contents: [{ role: 'user', parts }],
        generationConfig,
      });

      logger.info(`[parseMultipleImages][Gemini] - Response received`);

      // Get the response text
      const responseText = response.response.text();

      // Validate response
      if (!responseText) {
        throw new Error('Empty response from Gemini API');
      }

      // Extract JSON from response text
      const startIdx = responseText.indexOf('{');
      const endIdx = responseText.lastIndexOf('}');

      if (startIdx === -1 || endIdx === -1) {
        logger.error(`[parseMultipleImages][Gemini] - Invalid JSON response: ${responseText}`);
        throw new Error('Could not find JSON markers in response');
      }

      const jsonStr = responseText.slice(startIdx, endIdx + 1);

      // Parse the JSON
      try {
        return JSON.parse(jsonStr);
      } catch (parseError) {
        logger.error(`[parseMultipleImages][Gemini] - JSON parse error: ${parseError}`);
        throw new Error(`Failed to parse JSON response: ${parseError}`);
      }
    } catch (error) {
      logger.error(`[parseMultipleImages][Gemini] - Error during operation: ${JSON.stringify(error)}`);
      throw error; // Re-throw for the withRetry function to handle
    }
  });
};

/**
 * Generates text content based on a prompt using the Gemini API with retry logic.
 * Assumes the response should contain a parsable JSON object.
 * @param prompt The text prompt to send to the model.
 * @param generationConfig Optional generation configuration.
 * @returns The parsed JSON object from the model's response.
 */
export const generateTextContent = async (prompt: string, generationConfig?: Partial<GenerationConfig>) => {
  logger.info('[generateTextContent][Gemini] - Generating text content');

  // Default generation config
  const config: GenerationConfig = {
    temperature: 0,
    topK: 40,
    topP: 0.95,
    maxOutputTokens: GEMINI_MAX_OUTPUT_TOKENS,
    ...generationConfig,
  };

  // Sequentially try Gemini models
  const models = [GEMINI_MODEL, GEMINI_BACKOFF_MODEL];
  let lastError: any;

  for (const modelName of models) {
    try {
      logger.info(`[generateTextContent][Gemini] trying ${modelName}`);
      const client = genAI.getGenerativeModel({ model: modelName });

      const response = await client.generateContent({
        contents: [{ role: 'user', parts: [{ text: prompt }] }],
        generationConfig: config,
      });

      const text = response.response.text();
      if (!text) throw new Error('Empty response from Gemini');

      const start = text.indexOf('{');
      const end = text.lastIndexOf('}');
      if (start < 0 || end < 0) throw new Error('Invalid JSON response from Gemini');

      return JSON.parse(text.slice(start, end + 1));
    } catch (error: any) {
      lastError = error;
      logger.warn(`[generateTextContent][Gemini] ${modelName} failed: ${error.message}`);
    }
  }

  logger.error(`[generateTextContent][Gemini] All models failed: ${lastError.message}`);
  throw lastError;
};
