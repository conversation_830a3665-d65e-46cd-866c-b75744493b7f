# OneCarNow Backend v2 - Component Documentation

## Overview
This document provides comprehensive documentation for four key components in the OneCarNow backend system that handle digital signatures, payment processing, temporary subscriptions, and payment flow management.

---

## 1. Weetrust Integration

### Purpose
Weetrust Integration provides digital signature capabilities for contract documents, enabling electronic signing of vehicle rental agreements and addendums through a third-party digital signature service.

### Architecture
- **Main Service**: `src/modules/Associate/services/weetrust.ts`
- **Webhook Handler**: `src/modules/Webhooks/weetrust/`
- **Model**: `src/modules/Webhooks/weetrust/model/index.ts`

### Key Components

#### Authentication & Configuration
```typescript
// Environment Variables Required
WEETRUST_URL=<api_endpoint>
WEETRUST_USER_ID=<user_id>
WEETRUST_API_KEY=<api_key>
WEETRUST_ADD_ID=<additional_id>
```

#### Core Functions

**Token Management**
- `getWeetrustToken()`: Obtains access token for API authentication
- Token is used for all subsequent API calls

**Document Operations**
- `sendDocumentToWeetrust()`: Uploads PDF documents for signing
- `deleteWeetrustDocument()`: Removes documents from Weetrust platform
- `getDocumentById()`: Retrieves document information

**Webhook Processing**
- `receiveCompletedDocument`: Handles document completion events
- `receiveDocumentSignEvent`: Processes signature events from participants
- `retryProcessAdendumDocumentCompleted`: Manual retry mechanism for failed processes

### Workflow
1. **Document Upload**: PDF contracts are uploaded to Weetrust via API
2. **Signature Process**: Associates receive signing links and complete digital signatures
3. **Webhook Notifications**: Weetrust sends completion/signing events to backend
4. **Document Processing**: Completed documents are downloaded and stored in S3
5. **Database Updates**: Associate records are updated with signature status

### Integration Points
- **Associates**: Links to associate records for signature tracking
- **Stock Vehicles**: Updates vehicle status upon document completion
- **S3 Storage**: Stores completed signed documents
- **Document Management**: Integrates with internal document system

### Error Handling
- Webhook failures are logged and can be manually retried
- Failed document uploads include detailed error responses
- Automatic cleanup of failed document uploads

---

## 2. AssociatePayments

### Purpose
AssociatePayments manages the complete payment lifecycle for vehicle associates, including payment scheduling, tracking, history, and integration with multiple payment providers.

### Architecture
- **Model**: `src/models/associatePayments.ts`
- **Controllers**: `src/modules/AssociatePayments/controllers/`
- **Services**: `src/modules/AssociatePayments/services/`
- **Routes**: `src/modules/AssociatePayments/routes/`

### Data Structure

#### Core Fields
```typescript
interface AssociatePayments {
  vehiclesId: ObjectId;           // Reference to StockVehicle
  associateId: ObjectId;          // Reference to Associate
  contractId: ObjectId;           // Reference to MainContract
  associateEmail: string;         // Unique associate email
  
  // Payment Provider Integration
  gigId: string;                  // Gigstack client ID
  suscriptionId: string;          // Subscription identifier
  
  // Bank Account Information
  monexClabe: string;             // Monex bank account
  i80Clabe: string;              // I80 bank account
  oneCarNowClabe: string;        // OneCarNow bank account
  stpClabe: string;              // STP bank account
  soldClabe: string;             // Sold vehicle account
  
  // Payment Management
  balance: number;                // Current balance
  paymentNumber: number;          // Payment counter
  region: string;                 // Geographic region
  block: boolean;                 // Payment blocking status
}
```

#### Payment Arrays
- **paymentsArray**: Legacy payment structure with weekly costs and fees
- **newPaymentsArr**: Current payment structure with enhanced blocking capabilities
- **paymentsHistory**: Transaction history from various sources
- **otherPayment**: Additional payment records

### Key Operations

#### Payment Creation & Management
- **Create Associate Payment**: Links associate to payment system
- **Update Payment Status**: Manages payment states (pending, completed, blocked)
- **Block/Unblock Payments**: Controls payment processing
- **Payment History Tracking**: Records all payment transactions

#### Integration Services
- **Gigstack Integration**: Creates clients and manages recurring payments
- **Wire4 Integration**: Handles bank account creation and SPEI transfers
- **Monex Integration**: Manages traditional banking operations

#### Addendum Support
- **Divide Weeks**: Splits payment periods for contract modifications
- **Add Weeks**: Extends payment schedules
- **Temporary Items**: Manages temporary charges and fees

### Payment Flow Integration
- Links with StartPayFlow for payment initiation
- Integrates with TempSuscriptionPayments for temporary charges
- Connects to external payment APIs for processing

---

## 3. TempSuscriptionPayment

### Purpose
TempSuscriptionPayment handles temporary subscription modifications, allowing dynamic addition of temporary charges, payment schedule adjustments, and contract addendums without affecting the main subscription.

### Architecture
- **Model**: `src/modules/TempSuscriptionPayments/model/tempSuscriptionPayment.model.ts`
- **Controllers**: `src/modules/TempSuscriptionPayments/controllers/`
- **Routes**: `src/modules/TempSuscriptionPayments/routes/`
- **Cron Jobs**: Automated processing of temporary subscriptions

### Data Structure

```typescript
interface TempSuscriptionPayment {
  associateId: ObjectId;          // Reference to Associate
  associatePaymentId: ObjectId;   // Reference to AssociatePayments
  stockId: ObjectId;              // Reference to StockVehicle
  region: string;                 // Geographic region
  suscriptionId: string;          // Main subscription ID
  
  // Status Management
  status: 'active' | 'inactive' | 'pending';
  adendumType: 'divide-weeks' | 'add-weeks';
  
  // Date Management
  activationDate: Date;           // When to activate changes
  dateToAddTempProducts: Date;    // When to add temporary items
  stopDate: Date;                 // When to stop subscription
  
  // Temporary Items
  tempItems: [{
    name: string;
    description: string;
    quantity: number;
    temp: boolean;
    total: number;
    taxes: TaxInfo[];
    expirationDate: Date;
  }];
}
```

### Key Features

#### Temporary Item Management
- **Dynamic Addition**: Add temporary charges to existing subscriptions
- **Expiration Handling**: Automatic removal of expired items
- **Tax Calculation**: Proper tax handling for temporary items

#### Addendum Types
- **Divide Weeks**: Splits payment periods for contract modifications
- **Add Weeks**: Extends subscription duration
- **Custom Modifications**: Flexible temporary adjustments

#### Automated Processing
- **Cron Job Handler**: `cronJobTempPaymentsHandler` processes active subscriptions
- **Expiration Management**: Removes expired temporary items
- **Status Updates**: Automatically transitions between states

### Workflow
1. **Creation**: Temporary subscription created during addendum process
2. **Pending State**: Waits for activation date
3. **Activation**: Adds temporary items to payment system
4. **Processing**: Regular cron job checks for expirations
5. **Cleanup**: Removes expired items and deactivates when empty

### Integration Points
- **AssociatePayments**: Links to main payment records
- **Payment API**: Sends temporary items to external payment processor
- **Cron System**: Automated processing and cleanup

---

## 4. StartPayFlows

### Purpose
StartPayFlows manages the initiation and setup of payment subscriptions for new vehicle contracts, handling the transition from contract creation to active payment processing.

### Architecture
- **Model**: `src/models/start-pay-flow.ts`
- **Controllers**: `src/controllers/associate.ts` (startPayFlowController)
- **Integration**: Contract creation and payment system initialization

### Data Structure

```typescript
interface StartPayFlow {
  contractNumber: string;         // Contract identifier
  isCreated: boolean;            // Payment flow creation status
  
  // Date Range
  startDate: string;             // Payment start date
  endDate: string;               // Payment end date
  
  // References
  stockId: ObjectId;             // Reference to StockVehicle
  associateId: ObjectId;         // Reference to Associate
  clientId: string;              // Payment system client ID
  
  // Product Configuration
  rentingProduct: Mixed;         // Rental product details
  assistanceProduct: Mixed;      // Assistance product details (required for MX)
  downPaymentProduct: Mixed;     // Down payment configuration
  depositProduct: Mixed;         // Deposit configuration
}
```

### Key Operations

#### Flow Creation
- **Contract Integration**: Created during contract generation process
- **Product Configuration**: Sets up rental and assistance products
- **Date Calculation**: Determines payment schedule dates

#### Payment Initiation
- **Bank Account Setup**: Creates Monex CLABE for associate
- **Client Creation**: Establishes client in payment system
- **Subscription Setup**: Initiates recurring payment subscription

#### Flow Management
- **Status Tracking**: Monitors creation and activation status
- **Error Handling**: Manages failures in payment system integration
- **Cleanup**: Removes flows when contracts are cancelled

### Workflow
1. **Creation**: Flow created during contract generation
2. **Configuration**: Products and dates are configured
3. **Initiation**: `startPayFlowController` processes the flow
4. **Bank Setup**: Creates necessary bank accounts
5. **Subscription**: Establishes recurring payment subscription
6. **Activation**: Marks flow as created and activates payments
7. **Cleanup**: Flow removed if contract is cancelled

### Integration Points
- **Contract System**: Created during contract generation
- **Payment API**: Initiates subscriptions in external system
- **Banking**: Creates required bank accounts
- **AssociatePayments**: Links to ongoing payment management

### Error Handling
- **Validation**: Ensures all required data is present
- **API Failures**: Handles payment system integration errors
- **Retry Logic**: Supports manual retry of failed flows
- **Rollback**: Cleanup on failure scenarios

---

## Common Integration Patterns

### External API Integration
All components integrate with external payment and signature services:
- **Authentication**: Token-based authentication for all external APIs
- **Error Handling**: Comprehensive error logging and retry mechanisms
- **Webhook Processing**: Handles callbacks from external services

### Database Relationships
- **Associate-Centric**: All components link to Associate records
- **Vehicle Tracking**: Integration with StockVehicle for status updates
- **Document Management**: Links to Document system for file storage

### Monitoring & Logging
- **Winston Logging**: Comprehensive logging for all operations
- **Error Tracking**: Detailed error capture and reporting
- **Audit Trails**: Complete history of all payment and signature operations

### Security Considerations
- **Token Management**: Secure handling of API tokens and credentials
- **Data Validation**: Input validation for all external integrations
- **Access Control**: Proper authentication and authorization checks
