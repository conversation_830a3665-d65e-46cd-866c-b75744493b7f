# OneCarNow Backend v2 - Component Documentation

## Overview
This document provides comprehensive documentation for four key components in the OneCarNow backend system that handle digital signatures, payment processing, temporary subscriptions, and payment flow management.

---

## 1. Weetrust Integration

### Purpose
Weetrust Integration provides digital signature capabilities for contract documents, enabling electronic signing of vehicle rental agreements and addendums through a third-party digital signature service.

### Architecture
- **Main Service**: `src/modules/Associate/services/weetrust.ts`
- **Webhook Handler**: `src/modules/Webhooks/weetrust/`
- **Model**: `src/modules/Webhooks/weetrust/model/index.ts`

### Key Components

#### Authentication & Configuration
```typescript
// Environment Variables Required
WEETRUST_URL=<api_endpoint>
WEETRUST_USER_ID=<user_id>
WEETRUST_API_KEY=<api_key>
WEETRUST_ADD_ID=<additional_id>
```

#### Core Functions

**Token Management**
- `getWeetrustToken()`: Obtains access token for API authentication
- Token is used for all subsequent API calls

**Document Operations**
- `sendDocumentToWeetrust()`: Uploads PDF documents for signing
- `deleteWeetrustDocument()`: Removes documents from Weetrust platform
- `getDocumentById()`: Retrieves document information

**Webhook Processing**
- `receiveCompletedDocument`: Handles document completion events
- `receiveDocumentSignEvent`: Processes signature events from participants
- `retryProcessAdendumDocumentCompleted`: Manual retry mechanism for failed processes

### Detailed Flow Description

The Weetrust integration orchestrates the entire digital signature process for vehicle rental contracts. When a contract is ready for signing, the system first authenticates with Weetrust using API credentials to obtain an access token. The contract PDF is then uploaded to Weetrust's platform using a FormData request that includes the document file, language preferences (Spanish), geolocation positioning, country settings (Mexico), and electronic signature type configuration.

Once uploaded, Weetrust generates unique signing links for each participant (associate and OneCarNow representative). The associate receives notification through WhatsApp or email with their personalized signing link. When participants complete their signatures, Weetrust sends webhook notifications to the backend system.

The `receiveCompletedDocument` webhook handler processes these completion events by first saving the webhook data for audit purposes, then downloading the completed signed document from Weetrust's servers. The system extracts and processes the signed PDF, uploads it to AWS S3 for permanent storage, and updates the associate's digital signature status in the database. Additionally, it updates the vehicle's step status to reflect the completion of the signing process.

For addendum documents, a separate flow handles signature events through `receiveDocumentSignEvent`, which processes notifications when all participants have signed a document. This ensures that multi-party signature requirements are properly validated before considering a document fully executed.

### Integration Points
- **Associates**: Links to associate records for signature tracking
- **Stock Vehicles**: Updates vehicle status upon document completion
- **S3 Storage**: Stores completed signed documents
- **Document Management**: Integrates with internal document system

### Error Handling
- Webhook failures are logged and can be manually retried
- Failed document uploads include detailed error responses
- Automatic cleanup of failed document uploads

---

## 2. AssociatePayments

### Purpose
AssociatePayments manages the complete payment lifecycle for vehicle associates, including payment scheduling, tracking, history, and integration with multiple payment providers.

### Architecture
- **Model**: `src/models/associatePayments.ts`
- **Controllers**: `src/modules/AssociatePayments/controllers/`
- **Services**: `src/modules/AssociatePayments/services/`
- **Routes**: `src/modules/AssociatePayments/routes/`

### Data Structure

#### Core Fields
```typescript
interface AssociatePayments {
  vehiclesId: ObjectId;           // Reference to StockVehicle
  associateId: ObjectId;          // Reference to Associate
  contractId: ObjectId;           // Reference to MainContract
  associateEmail: string;         // Unique associate email

  // Payment Provider Integration
  gigId: string;                  // Gigstack client ID
  suscriptionId: string;          // Subscription identifier

  // Bank Account Information
  monexClabe: string;             // Monex bank account
  i80Clabe: string;              // I80 bank account
  oneCarNowClabe: string;        // OneCarNow bank account
  stpClabe: string;              // STP bank account
  soldClabe: string;             // Sold vehicle account

  // Payment Management
  balance: number;                // Current balance
  paymentNumber: number;          // Payment counter
  region: string;                 // Geographic region
  block: boolean;                 // Payment blocking status
}
```

#### Payment Arrays
- **paymentsArray**: Legacy payment structure with weekly costs and fees
- **newPaymentsArr**: Current payment structure with enhanced blocking capabilities
- **paymentsHistory**: Transaction history from various sources
- **otherPayment**: Additional payment records

### Detailed Flow Description

AssociatePayments serves as the central hub for managing all payment-related activities for vehicle associates throughout their rental lifecycle. When a new associate is onboarded, the system creates an AssociatePayments record that establishes the foundation for all future payment operations.

The process begins during contract creation when the system generates a comprehensive payment schedule based on the contract terms, vehicle type, and regional pricing models. This schedule is stored in the `paymentsArray` or `newPaymentsArr` depending on the contract version, with each entry containing weekly payment amounts, due dates, fees, and status information.

For payment provider integration, the system creates accounts across multiple platforms simultaneously. With Gigstack, it establishes a client profile using the associate's email and personal information, then sets up recurring payment schedules that automatically charge the associate's bank account weekly. The Wire4 integration handles SPEI (Mexican electronic transfer) capabilities by creating CLABE bank accounts that enable direct bank transfers. Monex integration provides traditional banking services and account management.

When payments are due, the system coordinates between these providers to process transactions. It monitors payment status through webhooks and API polling, updating the payment arrays with completion status, transaction IDs, and any failure reasons. The system also handles payment blocking scenarios where associates may have their payments suspended due to vehicle issues, late payments, or administrative holds.

For contract modifications, AssociatePayments integrates with addendum services to handle payment schedule changes. When contracts are extended or modified, the system can divide existing payment periods, add new payment weeks, or incorporate temporary charges without disrupting the existing payment flow.

### Key Operations

#### Payment Creation & Management
- **Create Associate Payment**: Links associate to payment system
- **Update Payment Status**: Manages payment states (pending, completed, blocked)
- **Block/Unblock Payments**: Controls payment processing
- **Payment History Tracking**: Records all payment transactions

#### Integration Services
- **Gigstack Integration**: Creates clients and manages recurring payments
- **Wire4 Integration**: Handles bank account creation and SPEI transfers
- **Monex Integration**: Manages traditional banking operations

#### Addendum Support
- **Divide Weeks**: Splits payment periods for contract modifications
- **Add Weeks**: Extends payment schedules
- **Temporary Items**: Manages temporary charges and fees

### Payment Flow Integration
- Links with StartPayFlow for payment initiation
- Integrates with TempSuscriptionPayments for temporary charges
- Connects to external payment APIs for processing

---

## 3. TempSuscriptionPayment

### Purpose
TempSuscriptionPayment handles temporary subscription modifications, allowing dynamic addition of temporary charges, payment schedule adjustments, and contract addendums without affecting the main subscription.

### Architecture
- **Model**: `src/modules/TempSuscriptionPayments/model/tempSuscriptionPayment.model.ts`
- **Controllers**: `src/modules/TempSuscriptionPayments/controllers/`
- **Routes**: `src/modules/TempSuscriptionPayments/routes/`
- **Cron Jobs**: Automated processing of temporary subscriptions

### Data Structure

```typescript
interface TempSuscriptionPayment {
  associateId: ObjectId;          // Reference to Associate
  associatePaymentId: ObjectId;   // Reference to AssociatePayments
  stockId: ObjectId;              // Reference to StockVehicle
  region: string;                 // Geographic region
  suscriptionId: string;          // Main subscription ID

  // Status Management
  status: 'active' | 'inactive' | 'pending';
  adendumType: 'divide-weeks' | 'add-weeks';

  // Date Management
  activationDate: Date;           // When to activate changes
  dateToAddTempProducts: Date;    // When to add temporary items
  stopDate: Date;                 // When to stop subscription

  // Temporary Items
  tempItems: [{
    name: string;
    description: string;
    quantity: number;
    temp: boolean;
    total: number;
    taxes: TaxInfo[];
    expirationDate: Date;
  }];
}
```

### Detailed Flow Description

TempSuscriptionPayment provides a sophisticated mechanism for handling temporary modifications to existing payment subscriptions without disrupting the main payment flow. This system is particularly crucial for managing contract addendums, temporary charges, and payment schedule adjustments that occur during the vehicle rental lifecycle.

When a contract modification is required, such as adding insurance coverage, extending rental periods, or applying temporary fees, the system creates a TempSuscriptionPayment record in "pending" status. This record contains all the temporary items with their respective costs, tax calculations, and expiration dates. The system calculates the appropriate activation dates, typically aligning with Monday payment cycles to maintain consistency with the existing payment schedule.

For "divide-weeks" addendums, the system temporarily pauses the main subscription and creates temporary payment items that cover the gap period. This is commonly used when vehicles require maintenance or when associates need temporary payment relief. The system calculates the exact dates when the main subscription should stop and restart, ensuring no payment overlap or gaps.

For "add-weeks" addendums, the system extends the subscription duration by creating temporary items that extend beyond the original contract end date. This handles scenarios where associates want to extend their rental period or when additional services are added to existing contracts.

The automated cron job system (`cronJobTempPaymentsHandler`) runs regularly to process these temporary subscriptions. It checks for activation dates, adds temporary items to the external payment system when due, monitors expiration dates, and removes expired items automatically. When all temporary items expire, the system transitions the subscription back to "inactive" status and ensures the main subscription resumes normal operation.

The system also handles complex scenarios like subscription pausing and resuming, where it can temporarily stop the main subscription on a specific date and reactivate it later, all while managing any temporary charges that apply during the pause period.

### Key Features

#### Temporary Item Management
- **Dynamic Addition**: Add temporary charges to existing subscriptions
- **Expiration Handling**: Automatic removal of expired items
- **Tax Calculation**: Proper tax handling for temporary items

#### Addendum Types
- **Divide Weeks**: Splits payment periods for contract modifications
- **Add Weeks**: Extends subscription duration
- **Custom Modifications**: Flexible temporary adjustments

#### Automated Processing
- **Cron Job Handler**: `cronJobTempPaymentsHandler` processes active subscriptions
- **Expiration Management**: Removes expired temporary items
- **Status Updates**: Automatically transitions between states

### Integration Points
- **AssociatePayments**: Links to main payment records
- **Payment API**: Sends temporary items to external payment processor
- **Cron System**: Automated processing and cleanup

---

## 4. StartPayFlows

### Purpose
StartPayFlows manages the initiation and setup of payment subscriptions for new vehicle contracts, handling the transition from contract creation to active payment processing.

### Architecture
- **Model**: `src/models/start-pay-flow.ts`
- **Controllers**: `src/controllers/associate.ts` (startPayFlowController)
- **Integration**: Contract creation and payment system initialization

### Data Structure

```typescript
interface StartPayFlow {
  contractNumber: string;         // Contract identifier
  isCreated: boolean;            // Payment flow creation status

  // Date Range
  startDate: string;             // Payment start date
  endDate: string;               // Payment end date

  // References
  stockId: ObjectId;             // Reference to StockVehicle
  associateId: ObjectId;         // Reference to Associate
  clientId: string;              // Payment system client ID

  // Product Configuration
  rentingProduct: Mixed;         // Rental product details
  assistanceProduct: Mixed;      // Assistance product details (required for MX)
  downPaymentProduct: Mixed;     // Down payment configuration
  depositProduct: Mixed;         // Deposit configuration
}
```

### Detailed Flow Description

StartPayFlows orchestrates the critical transition from signed contracts to active payment processing, serving as the bridge between contract completion and operational payment management. This component handles the complex initialization process that transforms a completed contract into a functioning payment subscription.

The process begins during contract generation when the system creates a StartPayFlow record with all necessary product configurations and payment schedule information. This record remains in a "not created" state (`isCreated: false`) until the contract is fully signed and ready for payment activation. The system configures rental products based on vehicle type and region, assistance products (mandatory for Mexican contracts), and optional down payment or deposit products based on contract terms.

When the `startPayFlowController` is triggered, it initiates a comprehensive setup process. First, it validates that all required components are in place - the associate exists, the vehicle is available, and the payment flow hasn't already been created. The system then creates the necessary banking infrastructure by generating a Monex CLABE (Mexican bank account number) for the associate, which enables direct bank transfers and SPEI payments.

The controller then communicates with the external payment API to establish the associate as a client in the payment system, updating their profile with the newly created bank account information. It constructs a comprehensive subscription request that includes all configured products, payment dates, and associate information, then submits this to the payment API to create the recurring payment subscription.

Upon successful subscription creation, the system updates the StartPayFlow record to mark it as created (`isCreated: true`), adds a history entry to the vehicle record documenting the payment activation, and establishes the link to the ongoing AssociatePayments system for future payment management.

The system includes robust error handling for various failure scenarios, such as bank account creation failures, payment API communication issues, or data validation problems. When contracts are cancelled or returned, the system automatically cleans up the StartPayFlow records to prevent orphaned payment configurations.

### Key Operations

#### Flow Creation
- **Contract Integration**: Created during contract generation process
- **Product Configuration**: Sets up rental and assistance products
- **Date Calculation**: Determines payment schedule dates

#### Payment Initiation
- **Bank Account Setup**: Creates Monex CLABE for associate
- **Client Creation**: Establishes client in payment system
- **Subscription Setup**: Initiates recurring payment subscription

#### Flow Management
- **Status Tracking**: Monitors creation and activation status
- **Error Handling**: Manages failures in payment system integration
- **Cleanup**: Removes flows when contracts are cancelled

### Integration Points
- **Contract System**: Created during contract generation
- **Payment API**: Initiates subscriptions in external system
- **Banking**: Creates required bank accounts
- **AssociatePayments**: Links to ongoing payment management

### Error Handling
- **Validation**: Ensures all required data is present
- **API Failures**: Handles payment system integration errors
- **Retry Logic**: Supports manual retry of failed flows
- **Rollback**: Cleanup on failure scenarios

---

## Common Integration Patterns

### External API Integration
All components integrate with external payment and signature services:
- **Authentication**: Token-based authentication for all external APIs
- **Error Handling**: Comprehensive error logging and retry mechanisms
- **Webhook Processing**: Handles callbacks from external services

### Database Relationships
- **Associate-Centric**: All components link to Associate records
- **Vehicle Tracking**: Integration with StockVehicle for status updates
- **Document Management**: Links to Document system for file storage

### Monitoring & Logging
- **Winston Logging**: Comprehensive logging for all operations
- **Error Tracking**: Detailed error capture and reporting
- **Audit Trails**: Complete history of all payment and signature operations

### Security Considerations
- **Token Management**: Secure handling of API tokens and credentials
- **Data Validation**: Input validation for all external integrations
- **Access Control**: Proper authentication and authorization checks
