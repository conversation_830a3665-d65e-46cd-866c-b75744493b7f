import mongoose, { Schema, Document } from 'mongoose';
import { ScheduleConfig } from '@/constants/vendor-platform';
import vendorDB from '@/vendor-platform/db';
import { IOrganization } from '../../organizations/models/organization.model';
import { VehicleStateType } from '@/models/StockVehicleSchema';

export interface IWorkshop extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  organizationId: mongoose.Types.ObjectId;
  organization: IOrganization;
  location: {
    address: string;
    city: string;
    country: string;
    mapsLink: string;
    state: VehicleStateType;
  };
  scheduleConfig?: ScheduleConfig;
}

export const scheduleConfigSchema = new Schema({
  weeklySchedule: {
    monday: { start: String, end: String },
    tuesday: { start: String, end: String },
    wednesday: { start: String, end: String },
    thursday: { start: String, end: String },
    friday: { start: String, end: String },
    saturday: { start: String, end: String },
    sunday: { start: String, end: String },
  },
  appointmentDuration: Number,
  maxSimultaneousAppointments: Number,
  timezone: String,
  breakTime: {
    start: { type: String, required: true },
    end: { type: String, required: true },
  },
  bufferTime: { type: Number },
  capacity: {
    totalBays: { type: Number, required: true },
    techniciansPerBay: { type: Number, required: true },
  },
});

const WorkshopSchema = new Schema(
  {
    name: { type: String, required: true },
    organizationId: { type: Schema.Types.ObjectId, ref: 'Organization', required: true },
    location: {
      address: { type: String, required: true },
      city: { type: String, required: true },
      country: { type: String, required: true },
      mapsLink: { type: String },
      state: { type: String },
    },
    // scheduleConfig: scheduleConfigSchema
    scheduleConfig: { type: scheduleConfigSchema },
    // State of the workshop, CDMX, MTY, GDL, etc
  },
  {
    timestamps: true,
  }
);

WorkshopSchema.index({ organizationId: 1, name: 1 }, { unique: true });

WorkshopSchema.virtual('organization', {
  ref: 'Organization',
  localField: 'organizationId',
  foreignField: '_id',
  justOne: true,
});

WorkshopSchema.set('toObject', { virtuals: true });
WorkshopSchema.set('toJSON', { virtuals: true });

export const Workshop = vendorDB.model<IWorkshop>('Workshop', WorkshopSchema);
