### Office Management API Tests
### Base URL: http://localhost:3000/office-management

### Variables
@baseUrl = http://localhost:3000/office-management
@token = Bearer YOUR_JWT_TOKEN_HERE

### ========================================
### AUTHENTICATION
### ========================================

### Get Google Auth URL
GET {{baseUrl}}/auth/google-url
Content-Type: application/json

### Google OAuth Callback
POST {{baseUrl}}/auth/google-callback
Content-Type: application/json

{
  "code": "google_oauth_code_here"
}

### Authenticate with Google ID Token
POST {{baseUrl}}/auth/google-login
Content-Type: application/json

{
  "idToken": "google_id_token_here"
}

### Verify Token
GET {{baseUrl}}/auth/verify
Authorization: {{token}}

### Get Profile
GET {{baseUrl}}/auth/profile
Authorization: {{token}}

### Logout
POST {{baseUrl}}/auth/logout
Authorization: {{token}}

### ========================================
### MEETING ROOMS
### ========================================

### Get All Meeting Rooms
GET {{baseUrl}}/meeting-rooms
Authorization: {{token}}

### Get Meeting Rooms with Availability
GET {{baseUrl}}/meeting-rooms/availability?date=2024-01-15
Authorization: {{token}}

### Get Meeting Room by ID
GET {{baseUrl}}/meeting-rooms/ROOM_ID_HERE
Authorization: {{token}}

### Create Meeting Room (Admin only)
POST {{baseUrl}}/meeting-rooms
Authorization: {{token}}
Content-Type: application/json

{
  "name": "Conference Room A",
  "location": {
    "building": "Main Building",
    "floor": "2nd Floor",
    "room": "Room 201",
    "address": "OneCarNow HQ"
  },
  "capacity": 10,
  "amenities": ["Projector", "Whiteboard", "Video Conference"],
  "equipment": ["TV", "HDMI Cable", "Markers"],
  "description": "Large conference room with video conferencing capabilities"
}

### Update Meeting Room (Admin only)
PUT {{baseUrl}}/meeting-rooms/ROOM_ID_HERE
Authorization: {{token}}
Content-Type: application/json

{
  "capacity": 12,
  "amenities": ["Projector", "Whiteboard", "Video Conference", "Coffee Machine"]
}

### Check Meeting Room Availability
POST {{baseUrl}}/meeting-rooms/ROOM_ID_HERE/check-availability
Authorization: {{token}}
Content-Type: application/json

{
  "startDate": "2024-01-15T14:00:00.000Z",
  "endDate": "2024-01-15T16:00:00.000Z"
}

### Get Meeting Room Calendar
GET {{baseUrl}}/meeting-rooms/ROOM_ID_HERE/calendar?startDate=2024-01-15&endDate=2024-01-22
Authorization: {{token}}

### Delete Meeting Room (Admin only)
DELETE {{baseUrl}}/meeting-rooms/ROOM_ID_HERE
Authorization: {{token}}

### ========================================
### DESKS
### ========================================

### Get All Desks
GET {{baseUrl}}/desks
Authorization: {{token}}

### Get Desks with Availability
GET {{baseUrl}}/desks/availability?date=2024-01-15
Authorization: {{token}}

### Get Available Desks for Date
GET {{baseUrl}}/desks/available?date=2024-01-15&type=individual&floor=2nd Floor
Authorization: {{token}}

### Get Desk by ID
GET {{baseUrl}}/desks/DESK_ID_HERE
Authorization: {{token}}

### Create Desk (Admin only)
POST {{baseUrl}}/desks
Authorization: {{token}}
Content-Type: application/json

{
  "name": "Desk A-01",
  "number": "A01",
  "location": {
    "building": "Main Building",
    "floor": "2nd Floor",
    "zone": "Zone A",
    "address": "OneCarNow HQ"
  },
  "type": "individual",
  "amenities": ["Monitor", "Keyboard", "Mouse"],
  "equipment": ["Laptop Stand", "USB Hub"],
  "description": "Individual desk with dual monitor setup"
}

### Update Desk (Admin only)
PUT {{baseUrl}}/desks/DESK_ID_HERE
Authorization: {{token}}
Content-Type: application/json

{
  "amenities": ["Monitor", "Keyboard", "Mouse", "Webcam"]
}

### Check Desk Availability
POST {{baseUrl}}/desks/DESK_ID_HERE/check-availability
Authorization: {{token}}
Content-Type: application/json

{
  "date": "2024-01-15"
}

### Get Desk Calendar
GET {{baseUrl}}/desks/DESK_ID_HERE/calendar?startDate=2024-01-15&endDate=2024-01-22
Authorization: {{token}}

### Delete Desk (Admin only)
DELETE {{baseUrl}}/desks/DESK_ID_HERE
Authorization: {{token}}

### ========================================
### BOOKINGS
### ========================================

### Create Meeting Room Booking
POST {{baseUrl}}/bookings
Authorization: {{token}}
Content-Type: application/json

{
  "resourceType": "meeting-room",
  "resourceId": "ROOM_ID_HERE",
  "title": "Team Meeting",
  "description": "Weekly team sync meeting",
  "startDate": "2024-01-15T14:00:00.000Z",
  "endDate": "2024-01-15T16:00:00.000Z",
  "attendees": {
    "internal": ["USER_ID_1", "USER_ID_2"],
    "external": ["<EMAIL>"]
  }
}

### Create Desk Booking
POST {{baseUrl}}/bookings
Authorization: {{token}}
Content-Type: application/json

{
  "resourceType": "desk",
  "resourceId": "DESK_ID_HERE",
  "title": "Work from Office",
  "description": "Working from office today",
  "startDate": "2024-01-15T08:00:00.000Z",
  "endDate": "2024-01-15T18:00:00.000Z"
}

### Get My Bookings
GET {{baseUrl}}/bookings/my?startDate=2024-01-01&endDate=2024-01-31&status=confirmed
Authorization: {{token}}

### Get Today's Bookings (Receptionist/Admin)
GET {{baseUrl}}/bookings/today
Authorization: {{token}}

### Get Booking Statistics
GET {{baseUrl}}/bookings/stats?startDate=2024-01-01&endDate=2024-01-31
Authorization: {{token}}

### Get All Bookings (Admin only)
GET {{baseUrl}}/bookings?startDate=2024-01-01&endDate=2024-01-31&resourceType=meeting-room
Authorization: {{token}}

### Get Booking by ID
GET {{baseUrl}}/bookings/BOOKING_ID_HERE
Authorization: {{token}}

### Update Booking
PUT {{baseUrl}}/bookings/BOOKING_ID_HERE
Authorization: {{token}}
Content-Type: application/json

{
  "title": "Updated Team Meeting",
  "description": "Updated description"
}

### Cancel Booking
PATCH {{baseUrl}}/bookings/BOOKING_ID_HERE/cancel
Authorization: {{token}}
Content-Type: application/json

{
  "reason": "Meeting cancelled due to schedule conflict"
}

### ========================================
### GENERAL
### ========================================

### Health Check
GET {{baseUrl}}/health
Content-Type: application/json

### Get Platform Info
GET {{baseUrl}}/info
Content-Type: application/json
