import { Types } from 'mongoose';
import { HttpException } from '@/vendor-platform/exceptions/HttpExceptions';
import { Quotation, IQuotation, QuotationStatus, IQuotationService } from '../models/quotation.model';
import {
  CorrectiveMaintenanceOrder,
  CorrectiveMaintenanceStatus,
} from '../models/corrective-maintenance-order.model';
import { CorrectiveService } from '../models/corrective-service.model';
import { logger } from '@/clean/lib/logger';
import { SlackQuotationNotificationsService } from './slack-quotation-notifications.service';

export interface CreateQuotationData {
  orderId: string;
  approvalType: 'fleet' | 'customer';
  approverEmail?: string;
  validityDays?: number;
  customerNotes?: string;
  internalNotes?: string;
  paymentTerms?: string;
  warrantyTerms?: string;
  services?: {
    serviceId: string;
    serviceName: string;
    description: string;
    estimatedCost: number;
    // laborCost: number; // Removed - not needed for vendor platform
    estimatedDuration: number;
    // partsCost?: number; // Removed - not needed for vendor platform
    serviceQuoteSummary: string;
    parts?: {
      name: string;
      quantity: number;
      // unitCost: number; // Removed - not needed for vendor platform
      totalCost: number;
      estimatedArrival?: Date;
    }[];
  }[];
}

export interface ApprovalDecision {
  serviceId: string;
  isApproved: boolean;
  rejectionReason?: string;
}

export class QuotationService {
  /**
   * Create a quotation from a diagnosed order or with custom services
   */
  async createQuotation(data: CreateQuotationData): Promise<IQuotation> {
    try {
      const order = await CorrectiveMaintenanceOrder.findById(data.orderId).populate('services');

      if (!order) {
        throw HttpException.NotFound('Corrective maintenance order not found');
      }

      // Check if there's already an active quotation
      const existingQuotation = await Quotation.findOne({
        orderId: order._id,
        status: { $in: [QuotationStatus.DRAFT, QuotationStatus.PENDING_APPROVAL] },
      });

      if (existingQuotation) {
        throw HttpException.BadRequest('Order already has an active quotation');
      }

      let quotationServices: IQuotationService[] = [];

      // If services are provided in the request, use them
      if (data.services && data.services.length > 0) {
        logger.info('📦 Creating quotation with services data:', {
          servicesCount: data.services.length,
          services: data.services.map((s) => ({
            serviceId: s.serviceId,
            serviceName: s.serviceName,
            partsCount: s.parts?.length || 0,
            parts: s.parts,
          })),
        });

        // Update the CorrectiveService documents with the new parts information
        for (const serviceData of data.services) {
          logger.info('🔄 Updating CorrectiveService with parts:', {
            serviceId: serviceData.serviceId,
            partsCount: serviceData.parts?.length || 0,
            parts: serviceData.parts,
          });

          const updatedService = await CorrectiveService.findByIdAndUpdate(
            serviceData.serviceId,
            {
              parts: serviceData.parts || [],
              totalPartsCost: 0, // No longer tracking parts cost
            },
            { new: true }
          );

          logger.info('✅ CorrectiveService updated:', {
            serviceId: serviceData.serviceId,
            updatedPartsCount: updatedService?.parts?.length || 0,
            success: !!updatedService,
          });
        }

        quotationServices = data.services.map((service) => ({
          serviceId: new Types.ObjectId(service.serviceId),
          serviceName: service.serviceName,
          description: service.description,
          estimatedCost: service.estimatedCost,
          estimatedDuration: service.estimatedDuration,
          // laborCost: service.laborCost, // Removed
          // partsCost: service.partsCost || 0, // Removed
          serviceQuoteSummary: service.description, // Default to description
          isApproved: false,
          parts: service.parts || [],
          slaEstimate: service.estimatedDuration,
          partsAvailabilityDelay: this.calculatePartsDelay(service.parts || []),
        }));
      } else {
        // Otherwise, use services from diagnosed order
        if (order.status !== CorrectiveMaintenanceStatus.DIAGNOSED) {
          throw HttpException.BadRequest(
            'Order must be diagnosed before creating quotation or provide services in request'
          );
        }

        if (!order.services || order.services.length === 0) {
          throw HttpException.BadRequest(
            'Order must have services before creating quotation or provide services in request'
          );
        }

        // Get services details
        const services = await CorrectiveService.find({
          _id: { $in: order.services },
        });

        // Build quotation services
        quotationServices = services.map((service) => ({
          serviceId: service._id,
          serviceName: service.serviceName,
          description: service.description,
          estimatedCost: service.estimatedCost,
          estimatedDuration: service.estimatedDuration,
          // laborCost: service.laborCost, // Removed
          // partsCost: service.totalPartsCost, // Removed
          serviceQuoteSummary: service.description, // Default to description
          isApproved: false,
          parts: service.parts.map((part) => ({
            name: part.name,
            quantity: part.quantity,
            // unitCost: part.unitCost, // Removed
            totalCost: 0, // Set to 0 - not tracking individual part costs
            estimatedArrival: part.estimatedArrival,
          })),
          slaEstimate: service.estimatedDuration,
          partsAvailabilityDelay: this.calculatePartsDelay(service.parts),
        }));
      }

      // Calculate validity date - removed as not needed
      // const validityDays = data.validityDays || 15;
      // const validUntil = new Date();
      // validUntil.setDate(validUntil.getDate() + validityDays);

      // Calculate earliest start date and completion date
      const earliestStartDate = new Date();
      earliestStartDate.setDate(earliestStartDate.getDate() + 1); // Next day

      const estimatedCompletionDate = new Date(earliestStartDate);
      const maxServiceTime = Math.max(
        ...quotationServices.map((s) => s.slaEstimate + (s.partsAvailabilityDelay || 0))
      );
      estimatedCompletionDate.setHours(estimatedCompletionDate.getHours() + maxServiceTime);

      // Create quotation
      const quotation = new Quotation({
        orderId: order._id,
        status: QuotationStatus.DRAFT,
        version: 1,
        services: quotationServices,
        totalEstimatedCost: 0, // Will be calculated by pre-save middleware
        totalEstimatedDuration: 0, // Will be calculated by pre-save middleware
        // totalLaborCost: 0, // Removed - not needed
        // totalPartsCost: 0, // Removed - not needed
        approvalRequired: true,
        approvalType: data.approvalType,
        approverEmail: data.approverEmail,
        approvedServices: [],
        rejectedServices: [],
        // validUntil, // Removed - not needed
        overallSLA: 0, // Will be calculated by pre-save middleware
        earliestStartDate,
        estimatedCompletionDate,
        paymentTerms: data.paymentTerms,
        warrantyTerms: data.warrantyTerms,
        customerNotes: data.customerNotes,
        internalNotes: data.internalNotes,
        diagnosticEvidence: {
          photos: order.diagnosisEvidence?.photos || [],
          videos: order.diagnosisEvidence?.videos || [],
          reports: [],
        },
      });

      await quotation.save();

      // Update order status
      order.status = CorrectiveMaintenanceStatus.QUOTED;
      await order.save();

      logger.info('Quotation created', {
        quotationId: quotation._id,
        orderId: order._id,
        servicesCount: quotationServices.length,
        totalCost: quotation.totalEstimatedCost,
      });

      return quotation;
    } catch (error) {
      logger.error('Error creating quotation', { error, data });
      throw error;
    }
  }

  /**
   * Submit quotation for approval
   */
  async submitForApproval(quotationId: string): Promise<IQuotation> {
    try {
      const quotation = await Quotation.findById(quotationId).populate('orderId');
      if (!quotation) {
        throw HttpException.NotFound('Quotation not found');
      }

      if (quotation.status !== QuotationStatus.DRAFT) {
        throw HttpException.BadRequest('Only draft quotations can be submitted for approval');
      }

      // Update quotation status
      quotation.status = QuotationStatus.PENDING_APPROVAL;
      quotation.submittedAt = new Date();
      await quotation.save();

      // Send Slack notification for fleet quotations
      if (quotation.approvalType === 'fleet') {
        try {
          await this.sendFleetQuotationSlackNotification(quotation);
        } catch (slackError) {
          logger.error('Error sending Slack notification for fleet quotation', {
            error: slackError,
            quotationId,
          });
          // Don't fail the quotation submission if Slack notification fails
        }
      }

      logger.info('Quotation submitted for approval', {
        quotationId,
        approvalType: quotation.approvalType,
        slackNotificationSent: quotation.approvalType === 'fleet',
      });

      return quotation;
    } catch (error) {
      logger.error('Error submitting quotation for approval', { error, quotationId });
      throw error;
    }
  }

  /**
   * Process approval decisions for individual services
   */
  async processApprovalDecisions(quotationId: string, decisions: ApprovalDecision[]): Promise<IQuotation> {
    try {
      const quotation = await Quotation.findById(quotationId);
      if (!quotation) {
        throw HttpException.NotFound('Quotation not found');
      }

      if (quotation.status !== QuotationStatus.PENDING_APPROVAL) {
        throw HttpException.BadRequest('Quotation is not pending approval');
      }

      // Process each decision
      for (const decision of decisions) {
        const serviceIndex = quotation.services.findIndex(
          (s) => s.serviceId.toString() === decision.serviceId
        );

        if (serviceIndex === -1) {
          throw HttpException.BadRequest(`Service ${decision.serviceId} not found in quotation`);
        }

        quotation.services[serviceIndex].isApproved = decision.isApproved;
        if (!decision.isApproved && decision.rejectionReason) {
          quotation.services[serviceIndex].rejectionReason = decision.rejectionReason;
        }

        // Update approved/rejected services arrays
        const serviceObjectId = new Types.ObjectId(decision.serviceId);
        if (decision.isApproved) {
          if (!quotation.approvedServices.includes(serviceObjectId)) {
            quotation.approvedServices.push(serviceObjectId);
          }
          quotation.rejectedServices = quotation.rejectedServices.filter((id) => !id.equals(serviceObjectId));
        } else {
          if (!quotation.rejectedServices.includes(serviceObjectId)) {
            quotation.rejectedServices.push(serviceObjectId);
          }
          quotation.approvedServices = quotation.approvedServices.filter((id) => !id.equals(serviceObjectId));
        }
      }

      // Determine final status
      const approvedCount = quotation.services.filter((s) => s.isApproved).length;
      const totalCount = quotation.services.length;

      if (approvedCount === 0) {
        quotation.status = QuotationStatus.REJECTED;
        quotation.rejectedAt = new Date();
      } else if (approvedCount === totalCount) {
        quotation.status = QuotationStatus.APPROVED;
        quotation.approvedAt = new Date();
      } else {
        quotation.status = QuotationStatus.PARTIALLY_APPROVED;
        quotation.approvedAt = new Date();
      }

      await quotation.save();

      // Update order status if approved
      if (
        quotation.status === QuotationStatus.APPROVED ||
        quotation.status === QuotationStatus.PARTIALLY_APPROVED
      ) {
        await CorrectiveMaintenanceOrder.findByIdAndUpdate(quotation.orderId, {
          status: CorrectiveMaintenanceStatus.APPROVED,
        });
      }

      logger.info('Approval decisions processed', {
        quotationId,
        approvedCount,
        totalCount,
        finalStatus: quotation.status,
      });

      return quotation;
    } catch (error) {
      logger.error('Error processing approval decisions', { error, quotationId, decisions });
      throw error;
    }
  }

  /**
   * Get quotations with filters
   */
  async getQuotations(filters: {
    orderId?: string;
    status?: QuotationStatus;
    approvalType?: 'fleet' | 'customer';
    page?: number;
    limit?: number;
  }) {
    try {
      const query: any = {};

      if (filters.orderId) query.orderId = filters.orderId;
      if (filters.status) query.status = filters.status;
      if (filters.approvalType) query.approvalType = filters.approvalType;

      const page = filters.page || 1;
      const limit = filters.limit || 20;
      const skip = (page - 1) * limit;

      const quotations = await Quotation.find(query)
        .populate({
          path: 'orderId',
          populate: {
            path: 'workshop',
            select: 'name location',
          },
        })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await Quotation.countDocuments(query);

      return {
        quotations,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error('Error getting quotations', { error, filters });
      throw error;
    }
  }

  /**
   * Calculate parts availability delay in hours
   */
  private calculatePartsDelay(parts: any[]): number {
    if (!parts || parts.length === 0) return 0;

    const now = new Date();
    let maxDelay = 0;

    for (const part of parts) {
      // Check both availability formats for compatibility
      const isNotAvailable = part.availability === 'unavailable' || !part.isAvailable;
      const arrivalDate = part.eta || part.estimatedArrival;

      if (isNotAvailable && arrivalDate) {
        const arrivalTime = new Date(arrivalDate);
        const delayHours = Math.max(0, (arrivalTime.getTime() - now.getTime()) / (1000 * 60 * 60));
        maxDelay = Math.max(maxDelay, delayHours);
      }
    }

    return Math.ceil(maxDelay);
  }

  /**
   * Send Slack notification for fleet quotation
   */
  private async sendFleetQuotationSlackNotification(quotation: IQuotation): Promise<void> {
    try {
      // Get order details
      const order = await CorrectiveMaintenanceOrder.findById(quotation.orderId)
        .populate('stockVehicle')
        .populate('associate')
        .populate('workshop');

      if (!order) {
        throw new Error('Order not found for quotation');
      }

      // Get vehicle information
      const vehicle = order.stockVehicle as any;
      const vehicleInfo = {
        brand: vehicle?.brand || 'N/A',
        model: vehicle?.model || 'N/A',
        year: vehicle?.year || 'N/A',
        carNumber: vehicle?.carNumber || 'N/A',
        plates: vehicle?.carPlates?.plates || 'N/A',
        vin: vehicle?.vin || 'N/A',
      };

      // Get customer information
      const associate = order.associate as any;
      const customerInfo = {
        name: associate?.name || associate?.firstName + ' ' + associate?.lastName || 'N/A',
        email: associate?.email || 'N/A',
        phone: associate?.phone,
      };

      // Get workshop information
      const workshop = order.workshop as any;
      const workshopInfo = {
        name: workshop?.name || 'N/A',
        location: workshop?.location || workshop?.address,
      };

      // Send Slack notification
      await SlackQuotationNotificationsService.sendFleetQuotationPendingNotification({
        quotation,
        order,
        vehicleInfo,
        customerInfo,
        workshopInfo,
      });

      logger.info('Fleet quotation Slack notification sent successfully', {
        quotationId: quotation._id,
        quotationNumber: quotation.quotationNumber,
      });
    } catch (error) {
      logger.error('Error sending fleet quotation Slack notification', {
        error,
        quotationId: quotation._id,
      });
      throw error;
    }
  }
}

export const quotationService = new QuotationService();
