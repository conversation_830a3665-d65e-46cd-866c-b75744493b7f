/* eslint-disable prettier/prettier */
/* eslint-disable max-params */
import { DateTime } from 'luxon';
import { IWorkshop, Workshop } from '../models/workshops.model';
import { AppointmentVendor, AppointmentVendorStatus, IAppointment } from '../models/appointment.model';
import { ScheduleConfig, WeeklySchedule, TimeRange, WorkshopCapacity } from '@/constants/vendor-platform';
import { IOrganization } from '../../organizations/models/organization.model';
import { ScheduleOverride } from '../../scheduleOverride/models/scheduleOverride.model';
import { ServiceTypeVendorModel } from '../../serviceType/models/serviceType.model';
import Associate from '@/models/associateSchema';
import StockVehicle from '@/models/StockVehicleSchema';
import { HttpException } from '@/vendor-platform/exceptions/HttpExceptions';
import { sendAppointmentNotificationMessage } from '../utils/sendHilosNotifications';
import { scheduleAppointmentReminder } from '../utils/createReminder';
import { getTimezoneOnCity } from '../utils/timezones_on_city';
import { MAINTENANCE_RULES, brandssNotAllowed } from '../utils/appointment-validation';
import MainContractSchema from '@/models/mainContractSchema';
import { sendSlackNotification } from '@/services/slackBotNotifier/slackMaintenanceChannel';
import { Types } from 'mongoose';
import { correctiveMaintenanceService } from '../../corrective-maintenance/services/corrective-maintenance.service';
import { CorrectiveMaintenanceType, FailureType, VehicleArrivalMethod } from '../../corrective-maintenance/models/corrective-maintenance-order.model';

const getDayKey = (dateTime: DateTime): keyof WeeklySchedule => {
  const day = dateTime.weekday;
  const days: Record<number, keyof WeeklySchedule> = {
    1: 'monday',
    2: 'tuesday',
    3: 'wednesday',
    4: 'thursday',
    5: 'friday',
    6: 'saturday',
    7: 'sunday',
  };
  return days[day] || 'monday';
};

type WorkshopWithOrganization = IWorkshop & { organization: IOrganization };

const KM_TOLERANCE = 1000; // Margen de 1000 km

/**
 * Maps service type name to CorrectiveServiceType enum value
 */

export class ScheduleService {
  // Método para obtener datos del workshop y organización, reemplazando el contexto anterior
  // siendo simplemente un método
  static async getWorkshopData(workshopId: string, functionName?: string) {
    const workshop = (await Workshop.findById(workshopId).populate(
      'organization'
    )) as WorkshopWithOrganization | null;
    if (!workshop) {
      throw HttpException.NotFound('Workshop not found');
    }
    console.log('Function:', functionName);

    return workshop;
  }

  static async getEffectiveScheduleConfig(workshop: WorkshopWithOrganization) {
    const organization = workshop.organization;

    if (!organization.globalScheduleConfig) {
      throw HttpException.NotFound('Organization global schedule config not found');
    }

    if (!workshop.scheduleConfig) {
      return organization.globalScheduleConfig;
    }

    const mergedConfig = this.mergeConfig(organization.globalScheduleConfig, workshop.scheduleConfig);
    return mergedConfig;
  }

  private static mergeConfig(organizationConfig: ScheduleConfig, workshopConfig: ScheduleConfig) {
    // validate if workshopConfig is not null to merge

    if (!workshopConfig) {
      return organizationConfig;
    }

    const mergeConfig = JSON.parse(JSON.stringify(organizationConfig));
    // Merge manual de las propiedades del workshop solo si existen
    Object.entries(workshopConfig).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (key === 'weeklySchedule' && value) {
          mergeConfig.weeklySchedule = {
            ...mergeConfig.weeklySchedule,
            ...value,
          };
        } else {
          mergeConfig[key as keyof ScheduleConfig] = value;
        }
      }
    });

    return mergeConfig;
  }

  private static isBreakTime(dateTime: DateTime, breakTime?: TimeRange): boolean {
    if (!breakTime) return false;

    const timeString = dateTime.toFormat('HH:mm');
    return timeString >= breakTime.start && timeString < breakTime.end;
  }

  static async getEffectiveScheduleConfigForDate(
    workshop: WorkshopWithOrganization,
    date: DateTime
  ): Promise<ScheduleConfig> {
    const organization = workshop.organization;

    const baseConfig = await this.mergeConfig(organization.globalScheduleConfig, workshop.scheduleConfig!);

    const overrideStartDate = date.set({
      hour: 0,
      minute: 0,
      second: 0,
      millisecond: 0,
    });

    const overrideEndDate = date.set({
      hour: 0,
      minute: 0,
      second: 0,
      millisecond: 0,
    });

    const override = await ScheduleOverride.findOne({
      type: 'MODIFIED',
      active: true,
      startDate: { $lte: overrideStartDate.toJSDate() },
      endDate: { $gte: overrideEndDate.toJSDate() },
    })
      .sort({ workshop: -1 }) // Priorizar overrides de taller
      .lean();

    if (!override?.modifiedSchedule) {
      return baseConfig;
    }
    return {
      ...baseConfig,
      ...(override.modifiedSchedule.workingHours && {
        weeklySchedule: {
          ...baseConfig.weeklySchedule,
          [getDayKey(date)]: override.modifiedSchedule.workingHours,
        },
      }),
      // merge breaktime and capacity from override and base config if override exists
      ...(override.modifiedSchedule.breakTime && {
        breakTime: override.modifiedSchedule.breakTime,
      }),
      ...(override.modifiedSchedule.capacity && {
        capacity: override.modifiedSchedule.capacity,
      }),
    };
  }

  private static async isDayBlocked(workshop: WorkshopWithOrganization, date: DateTime): Promise<boolean> {
    const blockStartDate = date.set({
      hour: 0,
      minute: 0,
      second: 0,
      millisecond: 0,
    });

    const blockEndDate = date.set({
      hour: 0,
      minute: 0,
      second: 0,
      millisecond: 0,
    });

    const blocked = await ScheduleOverride.findOne({
      type: 'BLOCKED',
      active: true,
      startDate: { $lte: blockStartDate.toJSDate() },
      endDate: { $gte: blockEndDate.toJSDate() },
      $and: [
        {
          $or: [{ organization: workshop.organization._id }, { workshop: workshop._id }],
        },
        {
          $or: [
            { scope: 'SINGLE' },
            {
              scope: 'YEARLY',
              $expr: {
                $and: [
                  { $eq: [{ $month: '$startDate' }, date.month] },
                  { $eq: [{ $dayOfMonth: '$startDate' }, date.day] },
                ],
              },
            },
          ],
        },
      ],
    });

    return !!blocked;
  }

  static async getAvailableSlots(
    workshopId: string,
    date: string,
    serviceTypeId: string
  ): Promise<DateTime[]> {
    const workshop = await this.getWorkshopData(workshopId, 'getAvailableSlots');

    const config = await this.getEffectiveScheduleConfigForDate(workshop, DateTime.fromISO(date));
    const targetDate = DateTime.fromISO(date?.split('T')[0] || date, {
      zone: config.timezone,
    });
    const isBlocked = await this.isDayBlocked(workshop, targetDate);
    if (isBlocked) return [];

    const serviceType = await ServiceTypeVendorModel.findById(serviceTypeId);
    if (!serviceType) {
      throw HttpException.NotFound('Service type not found');
    }

    const dayKey = getDayKey(targetDate);
    const daySchedule = config.weeklySchedule[dayKey];

    if (!daySchedule || !daySchedule.start || !daySchedule.end) return [];

    const slots: DateTime[] = [];
    let currentTime = targetDate.set({
      hour: parseInt(daySchedule.start.split(':')[0]),
      minute: parseInt(daySchedule.start.split(':')[1]),
    });

    const endTime = targetDate.set({
      hour: parseInt(daySchedule.end.split(':')[0]),
      minute: parseInt(daySchedule.end.split(':')[1]),
    });

    const now = DateTime.now().setZone(config.timezone);

    // Calcular margen dinámico basado en la duración del servicio
    const marginMinutes = Math.max(Math.floor(serviceType.duration / 6), 5);
    const minBookingTime = now.plus({ minutes: marginMinutes });

    // Tiempo total considerando la duración del servicio y el buffer
    const totalSlotDuration = serviceType.duration + (config.bufferTime || 0);

    while (currentTime < endTime) {
      if (targetDate.hasSame(now, 'day') && currentTime < minBookingTime) {
        currentTime = currentTime.plus({ minutes: serviceType.duration });
        continue;
      }

      if (this.isBreakTime(currentTime, config.breakTime)) {
        currentTime = currentTime.plus({ minutes: serviceType.duration });
        continue;
      }

      const serviceEndTime = currentTime.plus({ minutes: serviceType.duration });
      if (serviceEndTime > endTime) {
        break;
      }

      const isSlotAvailable = await this.isSlotAvailable(
        workshop,
        currentTime,
        serviceType.duration,
        config.capacity
      );

      if (isSlotAvailable) {
        slots.push(currentTime);
      }

      currentTime = currentTime.plus({ minutes: totalSlotDuration });
    }

    return slots;
  }

  static async sendAppointmentNotification(appointment: IAppointment) {
    const associate = await Associate.findById(appointment.associateId).select(
      'firstName lastName email phone city'
    );

    if (!associate) return;

    // Format date example: 25 de Enero de 2025, in spanish

    const date = DateTime.fromJSDate(appointment.startTime)
      .setZone('America/Mexico_City')
      .setLocale('es')
      .toFormat("dd 'de' LLLL 'de' yyyy");

    // Time format example: 10:00 AM

    const time = DateTime.fromJSDate(appointment.startTime)
      .setZone('America/Mexico_City')
      .toFormat('hh:mm a');

    const workshop = await Workshop.findById(appointment.workshopId).select('name location.mapsLink');

    if (!workshop) return;

    await sendAppointmentNotificationMessage({
      associateName: ` ${associate.firstName} ${associate.lastName}`,
      associatePhone: associate.phone.toString(),
      date: ` ${date}`, // empty space to separate the date from the template
      time,
      workshopName: workshop.name || '',
      // mapsLink: 'https://www.google.com/maps',
      mapsLink: workshop.location.mapsLink,
    });
  }

  static async validateMaintenanceRules(
    {
      brand,
      currentKm,
      serviceTypeId,
      previousKm,
      maintenanceNumber,
      stockVehicle,
      associate,
      adminCreatorEmail,
      maintenanceType = 'preventive', // Add maintenance type parameter
    }
      : {
        brand: string;
        currentKm: number;
        serviceTypeId: string;
        previousKm?: number;
        maintenanceNumber?: number;
        stockVehicle: any;
        associate: any;
        adminCreatorEmail?: string;
        maintenanceType?: 'preventive' | 'corrective';
      }
  ) {
    // Skip validation for corrective maintenance
    if (maintenanceType === 'corrective') {
      return; // Corrective maintenance doesn't follow the same rules as preventive
    }
    if (brandssNotAllowed.includes(brand.toUpperCase())) {
      throw HttpException.BadRequest('Modelo no permitido para mantenimiento');
    }

    console.table({
      brand,
      currentKm,
      serviceTypeId,
      previousKm,
      maintenanceNumber,
    });
    const rules = MAINTENANCE_RULES[brand] || MAINTENANCE_RULES.OTHERS;
    const serviceType = await ServiceTypeVendorModel.findById(serviceTypeId);

    if (!serviceType) throw HttpException.NotFound('Tipo de servicio inválido');

    // console.log('serviceType', serviceType.name);
    // console.log('--------------------------------------------------------------');

    // Verificar si estamos cerca del límite entre años
    const isNearYearBoundary = Math.abs(currentKm - rules.firstYear.maxKm) <= KM_TOLERANCE;
    // console.log('isNearYearBoundary', isNearYearBoundary);

    // Determinar año basado en kilometraje, pero considerando el caso especial del límite
    const isFirstYear = currentKm <= rules.firstYear.maxKm;
    // console.log('isFirstYear', isFirstYear);

    // Si estamos cerca del límite, obtener el mantenimiento más cercano
    let applicableRules;
    if (isNearYearBoundary) {
      // Si hay un número de mantenimiento específico, usarlo para validar
      if (maintenanceNumber) {
        // Buscar en ambos conjuntos de reglas
        const allIntervals = [...rules.firstYear.intervals, ...rules.secondYear.intervals];
        const maintenanceRule = allIntervals.find(
          (rule) => rule.maintenanceNumber === maintenanceNumber
        );

        if (!maintenanceRule) {
          throw HttpException.BadRequest(
            `No se encontró un mantenimiento correspondiente al número ${maintenanceNumber}`
          );
        }

        // Validar que el kilometraje actual esté dentro del rango permitido
        if (currentKm < maintenanceRule.km - KM_TOLERANCE) {
          const currentKmParsed = currentKm.toLocaleString('en-US').replace(/,/g, ',');
          const ruleKmParsed = maintenanceRule.km.toLocaleString('en-US').replace(/,/g, ',');
          const kmToleranceParsed = KM_TOLERANCE.toLocaleString('en-US').replace(/,/g, ',');
          const fromKm = maintenanceRule.km - KM_TOLERANCE;

          await sendSlackNotification.maintenanceError({
            userData: {
              firstName: associate.firstName,
              lastName: associate.lastName,
              phone: associate.phone.toString(),
            },
            vehicleData: {
              _id: stockVehicle._id.toString(),
              brand: stockVehicle.brand,
              model: stockVehicle.model,
              year: stockVehicle.year,
              version: stockVehicle.version,
              vin: stockVehicle.vin || 'N/A',
              contractNumber: (stockVehicle as any).contractNumber,
            },
            errorType: 'Kilometraje no alcanza el próximo mantenimiento requerido',
            errorDetails: `El kilometraje actual (${currentKmParsed} km) aún no corresponde al requerido para el mantenimiento ${maintenanceNumber}. Este debe realizarse a los ${ruleKmParsed} km, con un margen permitido de ±${kmToleranceParsed} km. Puede agendarse a partir de los ${fromKm} km.`,
            adminCreatorEmail,
          });

          throw HttpException.BadRequest(
            `El kilometraje actual (${currentKmParsed} km) aún no corresponde al requerido para el mantenimiento ${maintenanceNumber}. Este debe realizarse a los ${ruleKmParsed} km, con un margen permitido de ±${kmToleranceParsed} km. Puede agendarse a partir de los ${fromKm} km.`
          );
        }

        // Validar tipo de servicio
        if (!serviceType.name.toLowerCase().includes(maintenanceRule.serviceType.toLowerCase())) {
          throw HttpException.BadRequest(
            `Tipo de servicio incorrecto. Se requiere: ${maintenanceRule.serviceType.toUpperCase()}`
          );
        }

        return; // Validación exitosa
      } else {
        // Si no hay número de mantenimiento, usar el mantenimiento más cercano
        const maintenanceInfo = this.calculateMaintenance(brand, currentKm);
        applicableRules = [maintenanceInfo];
        // console.log('applicableRules (near boundary)', applicableRules);
      }
    } else {
      // Usar las reglas del año correspondiente
      applicableRules = isFirstYear ? rules.firstYear.intervals : rules.secondYear.intervals;
      // console.log('applicableRules', applicableRules[0]);
    }

    // console.log('maintenanceNumber', maintenanceNumber);

    // Si se proporciona el maintenanceNumber, buscar el intervalo correspondiente
    if (maintenanceNumber) {
      const maintenanceRule = applicableRules.find(
        (rule) => rule.maintenanceNumber === maintenanceNumber
      );

      if (!maintenanceRule) {
        throw HttpException.BadRequest(
          `No se encontró un mantenimiento correspondiente al número ${maintenanceNumber}`
        );
      }

      // Validar que el kilometraje actual esté dentro del rango permitido
      if (currentKm < maintenanceRule.km - KM_TOLERANCE) {
        const currentKmParsed = currentKm.toLocaleString('en-US').replace(/,/g, ',');
        const ruleKmParsed = maintenanceRule.km.toLocaleString('en-US').replace(/,/g, ',');
        const kmToleranceParsed = KM_TOLERANCE.toLocaleString('en-US').replace(/,/g, ',');
        const fromKm = maintenanceRule.km - KM_TOLERANCE;

        await sendSlackNotification.maintenanceError({
          userData: {
            firstName: associate.firstName,
            lastName: associate.lastName,
            phone: associate.phone.toString(),
          },
          vehicleData: {
            _id: stockVehicle._id.toString(),
            brand: stockVehicle.brand,
            model: stockVehicle.model,
            year: stockVehicle.year,
            version: stockVehicle.version,
            vin: stockVehicle.vin || 'N/A',
            contractNumber: (stockVehicle as any).contractNumber,
          },
          errorType: 'Kilometraje no alcanza el próximo mantenimiento requerido',
          errorDetails: `El kilometraje actual (${currentKmParsed} km) aún no corresponde al requerido para el mantenimiento ${maintenanceNumber}. Este debe realizarse a los ${ruleKmParsed} km, con un margen permitido de ±${kmToleranceParsed} km. Puede agendarse a partir de los ${fromKm} km.`,
          adminCreatorEmail,
        });

        throw HttpException.BadRequest(
          `El kilometraje actual (${currentKmParsed} km) aún no corresponde al requerido para el mantenimiento ${maintenanceNumber}. Este debe realizarse a los ${ruleKmParsed} km, con un margen permitido de ±${kmToleranceParsed} km. Puede agendarse a partir de los ${fromKm} km.`
        );
      }

      // Validar tipo de servicio
      if (!serviceType.name.toLowerCase().includes(maintenanceRule.serviceType.toLowerCase())) {
        throw HttpException.BadRequest(
          `Tipo de servicio incorrecto. Se requiere: ${maintenanceRule.serviceType.toUpperCase()}`
        );
      }

    } else if (typeof previousKm !== 'undefined') {
      // Validar secuencia de mantenimientos solo si hay registro previo
      const expectedRules = applicableRules.filter((rule) => rule.km > previousKm);
      const currentRule = expectedRules.find((rule) => rule.km <= currentKm);

      if (!currentRule) {
        throw HttpException.BadRequest('Kilometraje no alcanza el próximo mantenimiento requerido');
      }

      // Validar tipo de servicio
      if (!currentRule.serviceType.includes(serviceType.name)) {
        throw HttpException.BadRequest(
          `Tipo de servicio incorrecto. Se requiere: ${currentRule.serviceType}`
        );
      }
    } else {
      // Validación para primer mantenimiento
      const initialRule = applicableRules.find((rule) => rule.km <= currentKm);

      // console.log('--------------------------------------------------------------');
      // console.log('else cuando no hay previousKm');
      // console.log('initialRule', initialRule);
      // console.log('currentKm', currentKm);
      // console.log('currentKm < initialRule.km', !initialRule || currentKm < initialRule.km);
      // console.log('--------------------------------------------------------------');

      if (!initialRule || currentKm < initialRule.km) {
        const errorText = `Kilometraje inicial no válido para el primer mantenimiento ${isFirstYear ? 'del primer año' : 'del segundo año'}`;
        throw HttpException.BadRequest(errorText);
      }
    }
  }

  private static async validateAssociateAlreadyHasNearAppointment({
    associateId,
    stockId,
    km,
    startTime,
    serviceTypeId,
    adminCreatorEmail,
    registeredKm,
    isCorrectiveMaintenance = false,
  }: {
    associateId: string;
    stockId: string;
    km: number;
    startTime: string;
    serviceTypeId: string;
    adminCreatorEmail?: string;
    registeredKm: number;
    isCorrectiveMaintenance?: boolean;
  }) {
    const registeredKmParsed = registeredKm.toLocaleString('en-US').replace(/,/g, ',')
    const stockVehicle = await this.getStockVehicleData(stockId);

    const associate = stockVehicle.associates[stockVehicle.associates.length - 1];
    // console.log('[validateAssociateAlreadyHasNearAppointment] ---------------------------------');
    // console.table({
    //   associateId,
    //   stockId,
    //   km,
    //   startTime,
    //   serviceTypeId,
    //   adminCreatorEmail,
    //   registeredKm,
    // });

    const lastAppointment = await AppointmentVendor.findOne({
      associateId,
      stockId,
      // Only get appointments that are scheduled or rescheduled, to exclude canceled or other statuses
      status: { $in: [AppointmentVendorStatus.scheduled, AppointmentVendorStatus.rescheduled] },
    }).sort({ startTime: -1 });


    // Para mantenimiento correctivo, omitir todas las validaciones de kilometraje
    if (isCorrectiveMaintenance) {
      return {
        maintenanceNumber: 0, // Mantenimiento correctivo no sigue numeración secuencial
        maintenanceKm: km,
        requiredKm: km,
      };
    }

    // Primer mantenimiento: permitir sin restricciones
    if (!lastAppointment) {
      // Determinar el número de mantenimiento basado en el km actual
      const maintenanceInfo =
        this.calculateMaintenance(stockVehicle.brand.toUpperCase(), km)
      // Validar tipo de servicio pero permitir crear
      await this.validateMaintenanceRules(
        {
          brand: stockVehicle.brand.toUpperCase(),
          currentKm: km,
          serviceTypeId,
          stockVehicle,
          associate,
          adminCreatorEmail,
          maintenanceType: 'preventive', // Especificar que es preventivo
        }
      );

      // Retornar explícitamente el número de mantenimiento
      // return maintenanceNumber;
      return {
        maintenanceNumber: maintenanceInfo.maintenanceNumber,
        maintenanceKm: maintenanceInfo.km,
        requiredKm: km,
      }
    }

    // Mantenimientos posteriores
    const lastDate = DateTime.fromJSDate(lastAppointment.startTime);
    const currentDate = DateTime.fromISO(startTime);
    const monthsSinceLast = currentDate.diff(lastDate, 'months').months;


    const lastKm = lastAppointment.data?.registeredKm || stockVehicle.km;

    const lastMaintenanceNumber = lastAppointment.data?.maintenanceNumber ||
      this.calculateMaintenance(stockVehicle.brand.toUpperCase(), lastKm).maintenanceNumber;

    // Calcular el próximo mantenimiento requerido
    const nextMaintenanceInfo = this.calculateNextMaintenance(
      stockVehicle.brand.toUpperCase(),
      lastKm,
      lastMaintenanceNumber
    );
    const canScheduleByTime = monthsSinceLast >= 3;
    const canScheduleByKm = km >= nextMaintenanceInfo.km - KM_TOLERANCE;

    // Si cumple cualquiera de los dos criterios, puede agendar
    if (canScheduleByTime || canScheduleByKm) {
      // Validar tipo de servicio
      await this.validateMaintenanceRules(
        {
          brand: stockVehicle.brand.toUpperCase(),
          currentKm: km,
          serviceTypeId,
          previousKm: lastKm,
          maintenanceNumber: nextMaintenanceInfo.maintenanceNumber,
          stockVehicle,
          associate,
          adminCreatorEmail,
        }
      );

      // Retornar explícitamente el número de mantenimiento
      // return nextMaintenanceInfo.maintenanceNumber;
      return {
        maintenanceNumber: nextMaintenanceInfo.maintenanceNumber,
        maintenanceKm: nextMaintenanceInfo.km,
        requiredKm: nextMaintenanceInfo.km,
      }
    } else {
      // Not enough time or km
      const missingKm = nextMaintenanceInfo.km - KM_TOLERANCE - km;
      // Send slack notification
      await sendSlackNotification.maintenanceError({
        userData: {
          firstName: associate.firstName,
          lastName: associate.lastName,
          phone: associate.phone.toString(),
        },
        vehicleData: {
          _id: stockVehicle._id.toString(),
          brand: stockVehicle.brand,
          model: stockVehicle.model,
          year: stockVehicle.year,
          version: stockVehicle.version,
          vin: stockVehicle.vin || 'N/A',
          contractNumber: stockVehicle.contractNumber,
        },
        errorType: 'Cita agendada con fecha o kilometraje insuficiente',
        errorDetails: `La cita fue agendada con una fecha o kilometraje insuficiente (Se ingresó el kilometraje ${registeredKmParsed} km). Faltan ${Math.ceil(90 - currentDate.diff(lastDate, 'days').days)} días o ${missingKm} km más (mínimo ${nextMaintenanceInfo.km - KM_TOLERANCE} km)`,
        adminCreatorEmail,
      });

      throw HttpException.BadRequest(
        `Debe esperar ${Math.ceil(90 - currentDate.diff(lastDate, 'days').days)} días más ` +
        `o recorrer ${missingKm} km más (mínimo ${nextMaintenanceInfo.km - KM_TOLERANCE} km)`
      );
    }
  }

  static async getAssociateData(associateId: string) {
    const associate = await Associate.findById(associateId).select('firstName lastName phone');
    if (!associate) {
      throw HttpException.NotFound('Associate not found');
    }

    return associate;
  }

  static async getStockVehicleData(stockId: string) {
    const stockVehicle = await StockVehicle.findById(stockId)
      .select('brand model carNumber km _id drivers vehicleState extensionCarNumber carPlates.plates year version vin updateHistory')
      .populate({
        path: 'associates',
        select: 'firstName lastName email phone state city _id',
      })
      .lean();

    if (!stockVehicle) {
      throw HttpException.NotFound('Stock vehicle not found');
    }

    // return stockVehicle;
    return {
      ...stockVehicle,
      contractNumber: stockVehicle.extensionCarNumber
        ? `${stockVehicle.carNumber}-${stockVehicle.extensionCarNumber}`
        : stockVehicle.carNumber,
    }
  }

  static async createAppointment(
    workshopId: string,
    startTime: string,
    serviceTypeId: string,
    additionalData: {
      associateId: string;
      stockId: string;
      registeredKm: number;
      // Campos adicionales para mantenimiento correctivo
      failureDescription?: string;
      urgencyLevel?: 'low' | 'medium' | 'high' | 'critical';
      customerDescription?: string;
    },
    userId?: string,
    isAdminPlatform?: boolean,
    adminCreatorEmail?: string,
    isCorrectiveMaintenance?: boolean
  ) {
    // Esta función ahora retorna el número de mantenimiento
    const { maintenanceNumber, maintenanceKm } = await this.validateAssociateAlreadyHasNearAppointment({
      associateId: additionalData.associateId,
      stockId: additionalData.stockId,
      km: additionalData.registeredKm,
      startTime,
      serviceTypeId,
      adminCreatorEmail,
      registeredKm: additionalData.registeredKm,
      isCorrectiveMaintenance, // Pasar la bandera
    });

    const workshop = await this.getWorkshopData(workshopId, 'createAppointment');
    const config = await this.getEffectiveScheduleConfigForDate(workshop, DateTime.fromISO(startTime));
    const serviceType = await ServiceTypeVendorModel.findById(serviceTypeId);

    if (!serviceType) {
      throw HttpException.NotFound('Service type not found');
    }

    const startDateTime = DateTime.fromISO(startTime, {
      zone: config.timezone,
    });

    const isAvailable = await this.isSlotAvailable(
      workshop,
      startDateTime,
      serviceType.duration,
      config.capacity
    );

    if (!isAvailable) {
      throw HttpException.BadRequest('Selected time slot is no longer available');
    }

    const appointment = new AppointmentVendor({
      organizationId: workshop.organization._id,
      workshopId,
      startTime: startDateTime.toJSDate(),
      endTime: startDateTime.plus({ minutes: serviceType.duration }).toJSDate(),
      serviceTypeId,
      duration: serviceType.duration,
      status: AppointmentVendorStatus.scheduled,
      associateId: additionalData.associateId,
      stockId: additionalData.stockId,
      data: {
        registeredKm: additionalData.registeredKm,
        maintenanceNumber: maintenanceNumber, // Guardar el número de mantenimiento
        type: isCorrectiveMaintenance ? 'corrective' : 'preventive',
        // Campos adicionales para mantenimiento correctivo
        ...(isCorrectiveMaintenance && {
          failureDescription: additionalData.failureDescription,
          urgencyLevel: additionalData.urgencyLevel || 'medium',
        }),
      },
    });

    await this.sendAppointmentNotification(appointment);

    const associate = await this.getAssociateData(additionalData.associateId);
    const stockVehicle = await this.getStockVehicleData(additionalData.stockId);

    const timezone = getTimezoneOnCity(associate.city);
    await scheduleAppointmentReminder(
      {
        name: associate.firstName,
        phone: associate.phone.toString(),
        mapsLink: workshop.location.mapsLink,
        eventDate: startDateTime.toISO()!,
        workshopName: workshop.name,
      },
      timezone
    );

    await appointment.save();

    if (stockVehicle && isAdminPlatform && userId) {
      await StockVehicle.updateOne(
        { _id: stockVehicle._id },
        {
          $push: {
            updateHistory: {
              userId: new Types.ObjectId(userId),
              step: 'CITA AGENDADA',
              description: '',
            },
          },
        }
      );
    }
    // Si es mantenimiento correctivo, crear automáticamente el flujo completo
    if (isCorrectiveMaintenance) {
      try {
        await this.createCorrectiveMaintenanceFlow(
          appointment,
          workshop,
          additionalData,
          serviceType
        );
      } catch (error) {
        console.error('Error creating corrective maintenance flow:', error);
        // No fallar el appointment si hay error en el flujo correctivo
        // Solo loggear el error
      }
    }

    // Send slack notification of appointment created
    await sendSlackNotification.appointmentCreated({
      userData: {
        firstName: associate.firstName,
        lastName: associate.lastName,
        phone: associate.phone.toString(),
      },
      vehicleData: {
        _id: stockVehicle._id.toString(),
        brand: stockVehicle.brand,
        model: stockVehicle.model,
        year: stockVehicle.year,
        version: stockVehicle.version,
        vin: stockVehicle.vin || 'N/A',
        contractNumber: stockVehicle.contractNumber,
      },
      startTime: appointment.startTime.toISOString(),
      maintenanceType: isCorrectiveMaintenance
        ? `${serviceType.name} (Correctivo)`
        : serviceType.name,
      maintenanceNumber: maintenanceNumber,
      maintenanceKm: maintenanceKm,
      workshopName: workshop.name,
      adminCreatorEmail,
    });

    return appointment;
  }

  /**
   * Create the complete corrective maintenance flow when appointment is created
   */
  private static async createCorrectiveMaintenanceFlow(
    appointment: any,
    workshop: WorkshopWithOrganization,
    additionalData: {
      associateId: string;
      stockId: string;
      registeredKm: number;
      failureDescription?: string;
      urgencyLevel?: 'low' | 'medium' | 'high' | 'critical';
      customerDescription?: string;
    },
    serviceType: any
  ) {
    try {
      // 1. Crear orden de mantenimiento correctivo
      const correctiveOrder = await correctiveMaintenanceService.createOrder({
        stockId: additionalData.stockId,
        associateId: additionalData.associateId,
        organizationId: workshop.organization._id.toString(),
        workshopId: workshop._id.toString(),
        type: CorrectiveMaintenanceType.CUSTOMER_INITIATED,
        failureType: additionalData.failureDescription ? FailureType.KNOWN : FailureType.UNKNOWN,
        arrivalMethod: VehicleArrivalMethod.DRIVING, // Por defecto
        customerDescription: additionalData.customerDescription ||
          additionalData.failureDescription ||
          `Mantenimiento correctivo agendado - ${serviceType.name}`,
        canVehicleDrive: true,
        needsTowTruck: false,
        approvalType: 'fleet',
      });

      // 2. Actualizar el appointment con el ID de la orden correctiva
      await AppointmentVendor.findByIdAndUpdate(
        appointment._id,
        {
          'data.correctiveMaintenanceOrderId': correctiveOrder._id,
        }
      );

      // 3. Agregar notas iniciales pero NO completar el diagnóstico automáticamente
      // La orden debe permanecer en estado PENDING hasta que el taller haga el diagnóstico real

      // Solo agregar información inicial en las notas de la orden
      await correctiveMaintenanceService.addOrderNotes(
        correctiveOrder._id.toString(),
        `Orden creada automáticamente desde appointment para servicio: ${serviceType.name}. ` +
        `Duración estimada: ${serviceType.duration} minutos. ` +
        `Costo estimado: $${serviceType.price || 0}. ` +
        `Requiere diagnóstico por parte del taller.`
      );

      console.log('Corrective maintenance flow created successfully', {
        appointmentId: appointment._id,
        correctiveOrderId: correctiveOrder._id,
        serviceType: serviceType.name,
      });

      return correctiveOrder;
    } catch (error) {
      console.error('Error in createCorrectiveMaintenanceFlow:', error);
      throw error;
    }
  }

  /**
   * Create appointment specifically for corrective maintenance
   */
  static async createCorrectiveMaintenanceAppointment(
    workshopId: string,
    startTime: string,
    endTime: string,
    additionalData: {
      associateId: string;
      stockId: string;
      registeredKm: number;
      correctiveMaintenanceOrderId: string;
      failureDescription?: string;
      urgencyLevel?: 'low' | 'medium' | 'high' | 'critical';
    },
    serviceTypeId: string,
    userId?: string
  ) {
    const workshop = await this.getWorkshopData(workshopId, 'createCorrectiveMaintenanceAppointment');
    const config = await this.getEffectiveScheduleConfigForDate(workshop, DateTime.fromISO(startTime));
    const serviceType = await ServiceTypeVendorModel.findById(serviceTypeId);

    if (!serviceType) {
      throw HttpException.NotFound('Service type not found');
    }

    const startDateTime = DateTime.fromISO(startTime, {
      zone: config.timezone,
    });
    const endDateTime = DateTime.fromISO(endTime, {
      zone: config.timezone,
    });

    const duration = endDateTime.diff(startDateTime, 'minutes').minutes;

    // Check availability for corrective maintenance (may have different rules)
    const isAvailable = await this.isSlotAvailable(
      workshop,
      startDateTime,
      duration,
      config.capacity
    );

    if (!isAvailable) {
      throw HttpException.BadRequest('Selected time slot is no longer available');
    }

    const appointment = new AppointmentVendor({
      organizationId: workshop.organization._id,
      workshopId,
      startTime: startDateTime.toJSDate(),
      endTime: endDateTime.toJSDate(),
      serviceTypeId,
      duration: duration,
      status: AppointmentVendorStatus.scheduled,
      associateId: additionalData.associateId,
      stockId: additionalData.stockId,
      data: {
        registeredKm: additionalData.registeredKm,
        maintenanceNumber: 0, // Corrective maintenance doesn't follow sequential numbering
        type: 'corrective',
        correctiveMaintenanceOrderId: new Types.ObjectId(additionalData.correctiveMaintenanceOrderId),
        failureDescription: additionalData.failureDescription,
        urgencyLevel: additionalData.urgencyLevel || 'medium',
      },
    });

    await this.sendAppointmentNotification(appointment);

    const associate = await this.getAssociateData(additionalData.associateId);
    const stockVehicle = await this.getStockVehicleData(additionalData.stockId);

    const timezone = getTimezoneOnCity(associate.city);
    await scheduleAppointmentReminder(
      {
        name: associate.firstName,
        phone: associate.phone.toString(),
        mapsLink: workshop.location.mapsLink,
        eventDate: startDateTime.toISO()!,
        workshopName: workshop.name,
      },
      timezone
    );

    await appointment.save();

    if (stockVehicle && userId) {
      await StockVehicle.updateOne(
        { _id: stockVehicle._id },
        {
          $push: {
            updateHistory: {
              userId: new Types.ObjectId(userId),
              step: 'CITA MANTENIMIENTO CORRECTIVO AGENDADA',
              description: additionalData.failureDescription || 'Mantenimiento correctivo',
            },
          },
        }
      );
    }

    // Send slack notification for corrective maintenance appointment
    await sendSlackNotification.appointmentCreated({
      userData: {
        firstName: associate.firstName,
        lastName: associate.lastName,
        phone: associate.phone.toString(),
      },
      vehicleData: {
        _id: stockVehicle._id.toString(),
        brand: stockVehicle.brand,
        model: stockVehicle.model,
        year: stockVehicle.year,
        version: stockVehicle.version,
        vin: stockVehicle.vin || 'N/A',
        contractNumber: stockVehicle.contractNumber,
      },
      startTime: appointment.startTime.toISOString(),
      maintenanceType: `${serviceType.name} (Correctivo)`,
      maintenanceNumber: 0, // Corrective maintenance
      maintenanceKm: additionalData.registeredKm,
      workshopName: workshop.name,
      adminCreatorEmail: undefined, // No admin for corrective maintenance
    });

    return appointment;
  }

  static async getWorkshopAppointments(
    workshopId: string,
    startDate?: string,
    endDate?: string
  ): Promise<IAppointment[]> {
    // const query: any = {
    //   workshopId: workshopId,
    //   status: { $in: [AppointmentVendorStatus.scheduled, AppointmentVendorStatus.rescheduled] },
    // };
    const query: any = {
      workshopId: workshopId,
      // Get all apointments excluding canceled and not-attended
      status: { $nin: [AppointmentVendorStatus.canceled] },
    };

    if (startDate || endDate) {
      query.startTime = {};
      if (startDate) query.startTime.$gte = new Date(startDate);
      if (endDate) query.startTime.$lte = new Date(endDate);
    }

    return AppointmentVendor.find(query).sort({ startTime: 1 }).exec();
  }

  static async getOrganizationAppointments(organizationId: string, startDate?: string, endDate?: string) {
    const query: any = {
      organizationId,
      // Get all apointments excluding canceled and not-attended
      status: { $nin: [AppointmentVendorStatus.canceled] },
    };

    if (startDate || endDate) {
      query.startTime = {};
      if (startDate) {
        // check if time is not provided, if not, setting 00:00:00 to the start date
        const splitted = startDate.split('T');
        if (splitted.length === 1) {
          startDate = startDate + 'T00:00:00';
        }

        query.startTime.$gte = new Date(startDate);
      }
      if (endDate) {
        // check if time is not provided, if not, setting 23:59:59 to the end date
        const splitted = endDate.split('T');
        if (splitted.length === 1) {
          endDate = endDate + 'T23:59:59';
        }

        query.startTime.$lte = new Date(endDate);
      }

    }

    const appointment = await AppointmentVendor.find(query)
      // populate with select just required fields from appointment, workshop and service
      .select('startTime endTime status associateId stockId workshopId serviceTypeId data')
      .populate({
        path: 'workshop',
        select: 'name location color',
      })
      .populate({
        path: 'service',
        select: 'name description duration',
      })

      .sort({ startTime: 1 });

    if (!appointment) {
      throw HttpException.NotFound('No appointments found');
    }

    const appointmentsComplete = await Promise.all(
      appointment.map(async (appt) => {
        const associate = await Associate.findById(appt.associateId).select('firstName lastName email phone');
        if (!associate) {
          return {
            ...appt.toObject(),
          };
        }
        const stock = await StockVehicle.findById(appt.stockId).select(
          'carPlates brand model carNumber extensionCarNumber vin'
        );

        if (!stock) {
          return {
            ...appt.toObject(),
            associate,
          };
        }

        return {
          ...appt.toObject(),
          associate,
          stock,
        };
      })
    );

    return appointmentsComplete;
  }

  static async isSlotAvailable(
    workshop: WorkshopWithOrganization,
    startTime: DateTime,
    duration: number,
    capacity: WorkshopCapacity
  ): Promise<boolean> {
    const workshopId = workshop._id.toString();
    const endTime = startTime.plus({ minutes: duration });

    // Buscar citas solapadas
    const overlappingAppointments = await AppointmentVendor.find({
      workshopId: workshopId,
      status: { $in: [AppointmentVendorStatus.scheduled, AppointmentVendorStatus.rescheduled] },
      $or: [
        {
          startTime: {
            $gte: startTime.toJSDate(),
            $lt: endTime.toJSDate(),
          },
        },
        {
          endTime: {
            $gt: startTime.toJSDate(),
            $lte: endTime.toJSDate(),
          },
        },
        {
          startTime: { $lte: startTime.toJSDate() },
          endTime: { $gte: endTime.toJSDate() },
        },
      ],
    }).populate('service'); // Importante: necesitamos la duración del servicio

    // Verificar disponibilidad de bahías
    if (overlappingAppointments.length >= capacity.totalBays) {
      return false;
    }

    const totalAvailableManHours = Math.min(
      capacity.totalBays,
      capacity.techniciansPerBay * capacity.totalBays
    );

    let occupiedManHours = 0;
    overlappingAppointments.forEach((appointment) => {
      const appointmentDuration = (appointment.service as any).duration;
      occupiedManHours += appointmentDuration / 60;
    });

    const newServiceManHours = duration / 60;
    return occupiedManHours + newServiceManHours <= totalAvailableManHours;
  }

  /**
   * Encuentra el intervalo de mantenimiento más cercano al kilometraje dado
   * @param brand - Marca del vehículo
   * @param currentKm - Kilometraje actual
   * @param considerYearBoundary - Si debe considerar el límite entre años
   * @param lastMaintenanceNumber - Número del último mantenimiento (opcional)
   * @returns - Información del intervalo de mantenimiento
   */
  private static findClosestMaintenanceInterval(
    brand: string,
    currentKm: number,
    considerYearBoundary: boolean = true,
    lastMaintenanceNumber?: number
  ) {
    const rules = MAINTENANCE_RULES[brand] || MAINTENANCE_RULES.OTHERS;
    const isFirstYear = currentKm <= rules.firstYear.maxKm;
    const result = Math.abs(currentKm - rules.firstYear.maxKm) <= KM_TOLERANCE
    // console.log('result', result);
    // Determinar si estamos cerca del límite entre años
    const isNearYearBoundary = considerYearBoundary && result;
    // console.log('isNearYearBoundary', isNearYearBoundary);
    let intervals;

    if (isNearYearBoundary) {
      // Si estamos cerca del límite, considerar todos los intervalos
      intervals = [...rules.firstYear.intervals, ...rules.secondYear.intervals];
    } else {
      // Si no, usar solo los intervalos del año correspondiente
      intervals = isFirstYear ? rules.firstYear.intervals : rules.secondYear.intervals;
    }

    // Ordenar los intervalos por kilometraje
    const sortedIntervals = [...intervals].sort((a, b) => a.km - b.km);

    // Si se proporciona lastMaintenanceNumber, buscar el siguiente mantenimiento
    if (lastMaintenanceNumber !== undefined) {
      // Encontrar el siguiente intervalo basado en el número de mantenimiento
      const nextInterval = sortedIntervals.find(
        interval => interval.maintenanceNumber > lastMaintenanceNumber
      );

      if (nextInterval) {
        return {
          km: nextInterval.km,
          maintenanceNumber: nextInterval.maintenanceNumber,
          serviceType: nextInterval.serviceType,
        };
      }

      // Si no hay siguiente en este conjunto, devolver el último
      const lastInterval = sortedIntervals[sortedIntervals.length - 1];
      return {
        km: lastInterval.km,
        maintenanceNumber: lastInterval.maintenanceNumber,
        serviceType: lastInterval.serviceType,
      };
    }

    // Si no se proporciona lastMaintenanceNumber, encontrar el más cercano al kilometraje actual
    let closestInterval = sortedIntervals[0];
    let minDistance = Math.abs(currentKm - closestInterval.km);

    for (const interval of sortedIntervals) {
      const distance = Math.abs(currentKm - interval.km);
      if (distance < minDistance) {
        minDistance = distance;
        closestInterval = interval;
      }
    }

    return {
      km: closestInterval.km,
      maintenanceNumber: closestInterval.maintenanceNumber,
      serviceType: closestInterval.serviceType,
    };
  }

  /**
   * Determina el mantenimiento más cercano basado en el kilometraje actual
   * @param brand - Marca del vehículo
   * @param currentKm - Kilometraje actual
   * @returns - Mantenimiento correspondiente
   */
  private static calculateMaintenance(brand: string, currentKm: number) {
    return this.findClosestMaintenanceInterval(brand, currentKm);
  }

  /**
   * Calcula el siguiente mantenimiento basado en el último kilometraje y número de mantenimiento
   * @param brand - Marca del vehículo
   * @param lastKm - Último kilometraje registrado
   * @param lastMaintenanceNumber - Número del último mantenimiento
   * @returns - Información del siguiente mantenimiento
   */
  private static calculateNextMaintenance(
    brand: string,
    lastKm: number,
    lastMaintenanceNumber: number
  ) {
    return this.findClosestMaintenanceInterval(brand, lastKm, true, lastMaintenanceNumber);
  }

  /**
   * Valida si el kilometraje actual es suficiente para el primer mantenimiento
   * @param brand - Marca del vehículo
   * @param currentKm - Kilometraje actual
   * @returns - Información del mantenimiento correspondiente o lanza excepción si no cumple
   */
  private static validateFirstMaintenanceKm(
    brand: string,
    currentKm: number,
    { stockVehicle, associate, adminCreatorEmail }:
      { stockVehicle: any, associate: any, adminCreatorEmail?: string }
  ) {
    const rules = MAINTENANCE_RULES[brand] || MAINTENANCE_RULES.OTHERS;

    const applicableRules = rules.firstYear.intervals;

    // Ordenar los intervalos por kilometraje ascendente
    const sortedIntervals = [...applicableRules].sort((a, b) => a.km - b.km);

    // Obtener el primer intervalo de mantenimiento
    const firstInterval = sortedIntervals[0];

    // Margen de tolerancia para permitir agendar antes del kilometraje exacto

    // Verificar si el kilometraje actual es suficiente para el primer mantenimiento
    // considerando el margen de tolerancia



    if (currentKm < firstInterval.km - KM_TOLERANCE) {
    // Parsear de esto: 10000 a esto: 10,000
      const currentKmParsed = currentKm.toLocaleString('en-US').replace(/,/g, ',');
      const firstIntervalKmParsed = firstInterval.km.toLocaleString('en-US').replace(/,/g, ',');
      const kmToleranceParsed = KM_TOLERANCE.toLocaleString('en-US').replace(/,/g, ',');
      const fromKm = firstInterval.km - KM_TOLERANCE;

      sendSlackNotification.maintenanceError({
        userData: {
          firstName: associate.firstName,
          lastName: associate.lastName,
          phone: associate.phone.toString(),
        },
        vehicleData: {
          _id: stockVehicle._id.toString(),
          brand: stockVehicle.brand,
          model: stockVehicle.model,
          year: stockVehicle.year,
          version: stockVehicle.version,
          vin: stockVehicle.vin || 'N/A',
          contractNumber: (stockVehicle as any).contractNumber,
        },
        errorType: 'Kilometraje no alcanza el próximo mantenimiento requerido',
        errorDetails: `El kilometraje actual (${currentKmParsed} km) aún no corresponde al requerido para el primer mantenimiento. Este debe realizarse a los ${firstIntervalKmParsed} km, con un margen permitido de ±${kmToleranceParsed} km. Puede agendarse a partir de los ${fromKm} km.`,
        adminCreatorEmail,
      });


      throw HttpException.BadRequest(
        `El kilometraje actual (${currentKmParsed} km) aún no corresponde al requerido para el primer mantenimiento. Este debe realizarse a los ${firstIntervalKmParsed} km, con un margen permitido de ±${kmToleranceParsed} km. Puede agendarse a partir de los ${fromKm} km.`
      );
    }

    // Determinar el número de mantenimiento basado en el km actual
    const { maintenanceNumber } = this.calculateMaintenance(brand, currentKm);

    return {
      km: currentKm,
      maintenanceNumber: maintenanceNumber,
      serviceType: this.getServiceTypeByMaintenanceNumber(brand, maintenanceNumber),
    };
  }

  static async getStockVehicleByPlates(plates: string) {
    const stockVehicle = await StockVehicle.findOne({ 'carPlates.plates': new RegExp(`^${plates.trim()}$`, 'i') })
      .select('brand model carNumber km _id drivers vehicleState extensionCarNumber carPlates.plates year version vin')
      .populate({
        path: 'associates',
        select: 'firstName lastName email phone state city _id',
      })
      .lean();

    if (!stockVehicle) {
      throw HttpException.NotFound('Vehículo no encontrado con esas placas');
    }

    // return stockVehicle;
    return {
      ...stockVehicle,
      contractNumber: stockVehicle.extensionCarNumber
        ? `${stockVehicle.carNumber}-${stockVehicle.extensionCarNumber}`
        : stockVehicle.carNumber,
    }
  }

  /**
   * Obtiene la información necesaria para agendar una cita basada en las placas y kilometraje
   * @param plates - Placas del vehículo
   * @param currentKm - Kilometraje actual
   * @returns - Información necesaria para agendar (serviceTypeId, workshop, etc.)
   */
  static async getAppointmentInfoByPlatesAndKm({
    plates,
    currentKm,
    adminCreatorEmail,
    isCorrectiveMaintenance = false,
  }: {
    plates: string;
    currentKm: number;
    adminCreatorEmail?: string;
    isCorrectiveMaintenance?: boolean;
  }) {
    // 1. Obtener información del vehículo por placas
    const stockVehicle = await this.getStockVehicleByPlates(plates);

    const contractNumber = stockVehicle.extensionCarNumber
      ? `${stockVehicle.carNumber}-${stockVehicle.extensionCarNumber}`
      : stockVehicle.carNumber;

    if (stockVehicle.associates.length === 0) {
      throw HttpException.NotFound('No se encontró un asociado para este vehículo');
    }

    const associate = stockVehicle.associates[stockVehicle.associates.length - 1];

    // // 2. Obtener información del asociado
    if (!associate) {
      throw HttpException.NotFound('Asociado no encontrado');
    }

    const mainContract = await MainContractSchema.findOne({
      associatedId: associate._id,
      stockId: stockVehicle._id,
    })
      .select('deliveredDate deliveryData')
      .sort({
        createdAt: -1, // Obtener el contrato más reciente
      })


    if (!mainContract) {

      await sendSlackNotification.maintenanceError({
        userData: {
          firstName: associate.firstName,
          lastName: associate.lastName,
          phone: associate.phone.toString(),
        },
        vehicleData: {
          _id: stockVehicle._id.toString(),
          brand: stockVehicle.brand,
          model: stockVehicle.model,
          year: stockVehicle.year,
          version: stockVehicle.version,
          vin: stockVehicle.vin || 'N/A',
          contractNumber,
        },
        errorType: 'No se encontró contrato relacionado con el vehículo, consultar con el equipo de soporte Tech',
        errorDetails: `No se encontró un contrato para este vehículo y asociado. Placas: ${stockVehicle.carPlates?.plates!}, Asociado: ${associate.firstName} ${associate.lastName}`,
      });


      throw HttpException.NotFound('Este vehiculo no cuenta con un contrato activo, por favor contacte a soporte');
    }

    const deliveredDate = mainContract.deliveryData?.isoStringRealDate
      || mainContract.deliveredDate.toISOString();

    const deliveredDateTime = DateTime.fromISO(deliveredDate).setZone('America/Mexico_City');

    const currentDateTime = DateTime.now().setZone('America/Mexico_City');

    // Check if there is a difference of more than 1 year between the current date and the delivered date
    const differenceInYears = currentDateTime.diff(deliveredDateTime, 'years').years;

    // Get maintenance rules for the vehicle brand
    const rules = MAINTENANCE_RULES[stockVehicle.brand.trim().toUpperCase()] || MAINTENANCE_RULES.OTHERS;
    const firstYearMaxKm = rules.firstYear.maxKm;

    if (currentKm > firstYearMaxKm && differenceInYears < 1 && stockVehicle.newCar) { // Just for new cars
      await sendSlackNotification.maintenanceError({
        userData: {
          firstName: associate.firstName,
          lastName: associate.lastName,
          phone: associate.phone.toString(),
        },
        vehicleData: {
          _id: stockVehicle._id.toString(),
          brand: stockVehicle.brand,
          model: stockVehicle.model,
          year: stockVehicle.year,
          version: stockVehicle.version,
          vin: stockVehicle.vin || 'N/A',
          contractNumber,
        },
        errorType: 'Límite de kilometraje anual excedido',
        errorDetails: `El vehículo ha excedido el límite de kilometraje anual contratado.\nKilometraje actual: ${currentKm}\nFecha de entrega: ${deliveredDate}\nFecha actual: ${currentDateTime.toISO()}`,
      });

      throw HttpException.BadRequest('No es posible agendar una cita de mantenimiento debido a que se ha excedido el limite de kilometraje anual contratado. \n\n Soporte se estará comunicando con usted para agendar la cita de servicio');
    }

    // Checar si tiene una cita scheduled o rescheduled, si si, regresarlo para mostrar la información
    const appointment = await AppointmentVendor.findOne({
      associateId: associate._id,
      status: { $in: [AppointmentVendorStatus.scheduled, AppointmentVendorStatus.rescheduled] },
    }).populate('serviceType').populate('workshop', 'name location.mapsLink scheduleConfig.timezone ').lean();

    if (appointment) {
      (stockVehicle as any).drivers = undefined;
      (stockVehicle as any).associates = undefined;
      (stockVehicle as any).contractNumber = stockVehicle.carNumber + (stockVehicle.extensionCarNumber ? `-${stockVehicle.extensionCarNumber}` : '');

      const appointmentKm = appointment.data.registeredKm;

      const maintenanceKm = this.calculateMaintenance(stockVehicle.brand.toUpperCase(), appointmentKm).km;

      const enrichedAppointment = {
        ...appointment,
        associate,
        stockVehicle,
        maintenanceKm,
      }
      return {
        appointment: enrichedAppointment,
      }
    }


    // 3. Determinar el estado del vehículo para buscar talleres disponibles
    const vehicleState = stockVehicle.vehicleState || associate.state;
    // 4. Buscar talleres en el mismo estado
    const workshops = await Workshop.find({ 'location.state': vehicleState, isActive: true })
      .populate('organization', 'globalScheduleConfig')
      .select('name location scheduleConfig organizationId');

    if (!workshops || workshops.length === 0) {
      throw HttpException.NotFound(
        `Actualmente no hay talleres disponibles en tu estado para agendar mantenimientos mediante este sistema. Para programar una cita, por favor contacta directamente al equipo de soporte.`
      );
    }

    // 5. Obtener el último mantenimiento (si existe)
    const lastAppointment = await AppointmentVendor.findOne({
      stockId: stockVehicle._id,
      // Include only appointments that are completed, to exclude canceled or other statuses
      status: {
        $in: [AppointmentVendorStatus.completed],
      },
    }).sort({ startTime: -1 });


    // Para mantenimiento correctivo, omitir validaciones y devolver service types correctivos
    if (isCorrectiveMaintenance) {
      // Buscar service types de mantenimiento correctivo
      const correctiveServiceTypes = await ServiceTypeVendorModel.find({
        maintenanceType: 'corrective',
        isActive: true,
      });

      if (!correctiveServiceTypes || correctiveServiceTypes.length === 0) {
        throw HttpException.NotFound(
          'No se encontraron tipos de servicio de mantenimiento correctivo disponibles'
        );
      }

      // Obtener configuración de talleres
      const now = DateTime.now();
      const workshopsWithScheduleConfig = await Promise.all(
        workshops.map(async (workshop) => {
          const config = await this.getEffectiveScheduleConfigForDate(workshop, now);
          return {
            ...workshop.toObject(),
            scheduleConfig: config,
            organization: undefined,
            location: undefined,
          };
        })
      );

      // Limpiar datos del vehículo
      (stockVehicle.associates as any) = undefined;
      (stockVehicle.drivers as any) = undefined;
      (stockVehicle as any).contractNumber = stockVehicle.carNumber + (stockVehicle.extensionCarNumber ? `-${stockVehicle.extensionCarNumber}` : '');

      return {
        associate,
        stockVehicle,
        serviceTypes: correctiveServiceTypes,
        maintenanceNumber: 0, // Mantenimiento correctivo no sigue numeración
        maintenanceKm: currentKm,
        requiredKm: currentKm,
        workshops: workshopsWithScheduleConfig,
        isCorrectiveMaintenance: true,
      };
    }

    // 6. Calcular el número de mantenimiento actual y el próximo (solo para preventivo)
    let nextMaintenanceInfo;
    let canSchedule = true;
    let schedulingMessage = '';
    let maintenanceKm;
    if (!lastAppointment) {


      this.validateFirstMaintenanceKm(
        stockVehicle.brand.toUpperCase(),
        currentKm,
        { stockVehicle, associate, adminCreatorEmail }
      );

      // Determinar el número de mantenimiento basado en el km actual
      const { maintenanceNumber, km } =
        this.calculateMaintenance(stockVehicle.brand.toUpperCase(), currentKm);

      nextMaintenanceInfo = {
        km,
        maintenanceNumber: maintenanceNumber,
        serviceType: this.getServiceTypeByMaintenanceNumber(
          stockVehicle.brand.toUpperCase(), maintenanceNumber),
      };
      maintenanceKm = km;
    } else {
      // Mantenimientos posteriores
      const lastDate = DateTime.fromJSDate(lastAppointment.startTime);
      const currentDate = DateTime.now();
      const monthsSinceLast = currentDate.diff(lastDate, 'months').months;

      const lastKm = lastAppointment.data?.registeredKm || stockVehicle.km;
      const lastMaintenanceNumber = lastAppointment.data?.maintenanceNumber ||
        this.calculateMaintenance(stockVehicle.brand.toUpperCase(), lastKm).maintenanceNumber;

      maintenanceKm = this.calculateMaintenance(stockVehicle.brand.toUpperCase(), currentKm).km;

      nextMaintenanceInfo = this.calculateNextMaintenance(
        stockVehicle.brand.toUpperCase(),
        lastKm,
        lastMaintenanceNumber
      );

      // Aplicar las mismas validaciones que en createAppointment
      const canScheduleByTime = monthsSinceLast >= 3;
      const canScheduleByKm = currentKm >= nextMaintenanceInfo.km - KM_TOLERANCE;

      canSchedule = canScheduleByTime || canScheduleByKm;

      if (!canSchedule) {
        // const missingMonths = Math.ceil(3 - monthsSinceLast);
        const missingKm = nextMaintenanceInfo.km - KM_TOLERANCE - currentKm;

        const currentKmParsed = currentKm.toLocaleString('en-US').replace(/,/g, ',');

        await sendSlackNotification.maintenanceError({
          userData: {
            firstName: associate.firstName,
            lastName: associate.lastName,
            phone: associate.phone.toString(),
          },
          vehicleData: {
            _id: stockVehicle._id.toString(),
            brand: stockVehicle.brand,
            model: stockVehicle.model,
            year: stockVehicle.year,
            version: stockVehicle.version,
            vin: stockVehicle.vin || 'N/A',
            contractNumber: stockVehicle.contractNumber,
          },
          errorType: 'Cita agendada con fecha o kilometraje insuficiente',
          errorDetails: `La cita fue agendada con una fecha o kilometraje insuficiente (Se ingresó el kilometraje ${currentKmParsed} km). Faltan ${Math.ceil(90 - currentDate.diff(lastDate, 'days').days)} días más o ${missingKm} km más (mínimo ${nextMaintenanceInfo.km - KM_TOLERANCE} km)`,
          adminCreatorEmail,
        });


        schedulingMessage = `Debe esperar ${Math.ceil(90 - currentDate.diff(lastDate, 'days').days)} días más o recorrer ${missingKm} km más (mínimo ${nextMaintenanceInfo.km - KM_TOLERANCE} km)`;
        throw HttpException.BadRequest(schedulingMessage);
      }
    }

    // 7. Determinar el serviceTypeId basado en la marca y el kilometraje
    let serviceTypeQuery;

    if (stockVehicle.brand.toUpperCase() === 'BYD') {
      // Para BYD, buscar por el formato específico "BYD X,XXX km"
      const formattedKm = nextMaintenanceInfo.km.toLocaleString('en-US').replace(/,/g, ',');
      serviceTypeQuery = { name: new RegExp(`BYD ${formattedKm}km`, 'i') };
    } else {
      // Para otras marcas, buscar por el tipo de servicio
      serviceTypeQuery = { name: new RegExp(nextMaintenanceInfo.serviceType, 'i') };
    }
    const serviceTypes = await ServiceTypeVendorModel.find(serviceTypeQuery);

    if (!serviceTypes || serviceTypes.length === 0) {
      throw HttpException.NotFound(
        `No se encontró un tipo de servicio adecuado para el mantenimiento requerido`
      );
    }

    // 8. Obtener el workshop principal (el primero de la lista por ahora)
    // const workshop = workshops[0];

    // 9. Obtener el scheduleConfig mergeado
    const now = DateTime.now();
    // const config = await this.getEffectiveScheduleConfigForDate(workshop, now);

    const workshopsWithScheduleConfig = await Promise.all(
      workshops.map(async (workshop) => {
        const config = await this.getEffectiveScheduleConfigForDate(workshop, now);
        return {
          ...workshop.toObject(),
          scheduleConfig: config,
          organization: undefined,
          location: undefined,
        };
      })
    );

    // delete stockVehicle.associates;
    (stockVehicle.associates as any) = undefined;
    (stockVehicle.drivers as any) = undefined;
    (stockVehicle as any).contractNumber = stockVehicle.carNumber + (stockVehicle.extensionCarNumber ? `-${stockVehicle.extensionCarNumber}` : '');



    // 10. Preparar la respuesta
    return {
      associate,
      stockVehicle,
      // serviceTypeId: serviceType._id,
      serviceTypes: serviceTypes,
      maintenanceNumber: nextMaintenanceInfo.maintenanceNumber,
      maintenanceKm,
      requiredKm: nextMaintenanceInfo.km,
      workshops: workshopsWithScheduleConfig,
      //
    };
  }

  /**
   * Obtiene el tipo de servicio basado en el número de mantenimiento
   * @param brand - Marca del vehículo
   * @param maintenanceNumber - Número de mantenimiento
   * @returns - Tipo de servicio correspondiente
   */
  private static getServiceTypeByMaintenanceNumber(brand: string, maintenanceNumber: number): string {
    const rules = MAINTENANCE_RULES[brand] || MAINTENANCE_RULES.OTHERS;
    const firstYearIntervals = rules.firstYear.intervals;
    const secondYearIntervals = rules.secondYear.intervals;

    // Buscar en los intervalos del primer año
    const firstYearMatch = firstYearIntervals.find(
      interval => interval.maintenanceNumber === maintenanceNumber
    );

    if (firstYearMatch) {
      return firstYearMatch.serviceType;
    }

    // Buscar en los intervalos del segundo año
    const secondYearMatch = secondYearIntervals.find(
      interval => interval.maintenanceNumber === maintenanceNumber
    );

    if (secondYearMatch) {
      return secondYearMatch.serviceType;
    }

    // Si no se encuentra, usar el último intervalo del segundo año
    return secondYearIntervals[secondYearIntervals.length - 1].serviceType;
  }
}
