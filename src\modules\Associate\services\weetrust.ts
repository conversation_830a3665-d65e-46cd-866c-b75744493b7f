import axios from 'axios';
import { WEETRUST_API_KEY, WEETRUST_URL, WEETRUST_USER_ID } from '../../../constants';
import FormData from 'form-data';
import fs from 'fs';
import { logger } from '../../../clean/lib/logger';

export default async function getWeetrustToken() {
  const response = await axios.post(
    `${WEETRUST_URL}/access/token`,
    {},
    {
      headers: {
        'user-id': WEETRUST_USER_ID,
        'api-key': WEETRUST_API_KEY,
      },
    }
  );
  return response.data.responseData.accessToken;
}

export async function deleteWeetrustDocument(token: string | undefined, documentId: string) {
  try {
    if (!token) {
      token = await getWeetrustToken();
    }

    const url = new URL(WEETRUST_URL + '/documents');

    url.searchParams.append('documentID', documentId);

    const { data } = await axios.delete(url.toString(), {
      headers: {
        'user-id': WEETRUST_USER_ID,
        token,
      },
    });
    logger.info(`[deleteWeetrustDocument]: Document deleted successfully ${documentId}`);

    return data;
  } catch (error: any) {
    // console.error(error.response.data);
    const message = error.response?.data || error.message;
    const errorObj = {
      message,
      documentId,
      error: error.response.data,
    };
    logger.error(`[deleteWeetrustDocument]: ${JSON.stringify(errorObj)}`);
    return {
      success: false,
      ...errorObj,
    };
  }
}

export async function sendDocumentToWeetrust({ file, token }: { file: Express.Multer.File; token?: string }) {
  try {
    if (!token) {
      token = await getWeetrustToken();
    }

    const url = new URL(WEETRUST_URL + '/documents');

    const language = 'es';

    const formData = new FormData();

    const fileStream = fs.createReadStream(file.path);

    formData.append('document', fileStream, {
      filename: file.originalname,
      contentType: 'application/pdf',
    });

    const { data } = await axios.post(url.toString(), formData, {
      headers: {
        'user-id': WEETRUST_USER_ID,
        ...formData.getHeaders(),
        token,
        language,
        position: 'geolocation',
        country: 'mx',
        documentSignType: 'ELECTRONIC_SIGNATURE',
      },
    });

    return data;
  } catch (error: any) {
    // console.error(error.response.data);
    console.log('error', error.response?.data || error.response || error.message);
    return null;
  }
}

export async function getDocumentById(documentId: string, useToken?: string) {
  try {
    // const token = await getWeetrustToken();
    const token = useToken || (await getWeetrustToken());

    const url = new URL(WEETRUST_URL + '/documents/' + documentId);

    // url.searchParams.append('documentID', documentId);

    const { data } = await axios.get(url.toString(), {
      headers: {
        'user-id': WEETRUST_USER_ID,
        token,
      },
    });

    return data;
  } catch (error: any) {
    // console.error(error.response.data);
    console.log('error', error.response?.data || error.response || error.message);
    return null;
  }
}
