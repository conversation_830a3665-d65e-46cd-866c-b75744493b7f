import { BookingModel, IB<PERSON>ing, MeetingRoomModel, DeskModel } from '../../../models';
import { HttpException } from '../../../exceptions/HttpExceptions';
import { HttpStatus } from '../../../exceptions/HttpStatus';
import { DateHelpers } from '../../../utils/date-helpers';
import { MeetingRoomsService } from '../../meeting-rooms/services/meeting-rooms.service';
import { DesksService } from '../../desks/services/desks.service';

export class BookingsService {
  private meetingRoomsService = new MeetingRoomsService();
  private desksService = new DesksService();

  /**
   * Create a new booking
   */
  async createBooking(data: {
    userId: string;
    resourceType: 'meeting-room' | 'desk';
    resourceId: string;
    title: string;
    description?: string;
    startDate: Date;
    endDate: Date;
    attendees?: {
      internal?: string[];
      external?: string[];
    };
  }): Promise<IBooking> {
    // Validate resource exists
    if (data.resourceType === 'meeting-room') {
      await this.meetingRoomsService.getMeetingRoomById(data.resourceId);
    } else {
      await this.desksService.getDeskById(data.resourceId);
    }

    // Check availability
    const availability = await this.checkResourceAvailability(
      data.resourceType,
      data.resourceId,
      data.startDate,
      data.endDate,
      data.userId
    );

    if (!availability.available) {
      throw HttpException.BadRequest(`Resource not available: ${availability.conflicts?.join(', ')}`);
    }

    // For desk bookings, ensure it's a full day booking
    if (data.resourceType === 'desk') {
      const startOfDay = DateHelpers.getStartOfDay(data.startDate);
      const endOfDay = DateHelpers.getEndOfDay(data.startDate);
      
      data.startDate = startOfDay;
      data.endDate = endOfDay;
    }

    try {
      const booking = new BookingModel({
        ...data,
        status: 'confirmed',
        notifications: {
          slack: { sent: false },
          email: { sent: false }
        }
      });

      const savedBooking = await booking.save();
      
      // Populate the resource and user data
      await savedBooking.populate([
        { path: 'resourceId' },
        { path: 'userId', select: 'name email' },
        { path: 'attendees.internal', select: 'name email' }
      ]);

      return savedBooking;
    } catch (error: any) {
      throw HttpException.BadRequest(error.message);
    }
  }

  /**
   * Get user's bookings
   */
  async getUserBookings(
    userId: string,
    options: {
      startDate?: Date;
      endDate?: Date;
      status?: string[];
      resourceType?: 'meeting-room' | 'desk';
      limit?: number;
      skip?: number;
    } = {}
  ): Promise<IBooking[]> {
    const query: any = { userId };

    if (options.startDate || options.endDate) {
      query.startDate = {};
      if (options.startDate) query.startDate.$gte = options.startDate;
      if (options.endDate) query.startDate.$lte = options.endDate;
    }

    if (options.status) {
      query.status = { $in: options.status };
    }

    if (options.resourceType) {
      query.resourceType = options.resourceType;
    }

    return await BookingModel.find(query)
      .populate('resourceId')
      .populate('userId', 'name email')
      .populate('attendees.internal', 'name email')
      .sort({ startDate: -1 })
      .limit(options.limit || 50)
      .skip(options.skip || 0);
  }

  /**
   * Get booking by ID
   */
  async getBookingById(id: string, userId?: string): Promise<IBooking> {
    const query: any = { _id: id };
    
    // If userId is provided, ensure user can only see their own bookings or bookings they're invited to
    if (userId) {
      query.$or = [
        { userId: userId },
        { 'attendees.internal': userId }
      ];
    }

    const booking = await BookingModel.findOne(query)
      .populate('resourceId')
      .populate('userId', 'name email')
      .populate('attendees.internal', 'name email')
      .populate('guests');

    if (!booking) {
      throw HttpException.NotFound('Booking not found');
    }

    return booking;
  }

  /**
   * Update booking
   */
  async updateBooking(
    id: string,
    userId: string,
    data: Partial<IBooking>
  ): Promise<IBooking> {
    const booking = await this.getBookingById(id, userId);

    // Only the booking owner can update
    if (booking.userId.toString() !== userId) {
      throw HttpException.Forbidden('You can only update your own bookings');
    }

    // Don't allow updating past bookings
    if (DateHelpers.isInPast(booking.startDate)) {
      throw HttpException.BadRequest('Cannot update past bookings');
    }

    // If changing dates, check availability
    if (data.startDate || data.endDate) {
      const newStartDate = data.startDate || booking.startDate;
      const newEndDate = data.endDate || booking.endDate;

      const availability = await this.checkResourceAvailability(
        booking.resourceType,
        booking.resourceId.toString(),
        newStartDate,
        newEndDate,
        userId,
        id // Exclude current booking from conflict check
      );

      if (!availability.available) {
        throw HttpException.BadRequest(`Resource not available: ${availability.conflicts?.join(', ')}`);
      }
    }

    try {
      const updatedBooking = await BookingModel.findByIdAndUpdate(
        id,
        { ...data, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).populate([
        { path: 'resourceId' },
        { path: 'userId', select: 'name email' },
        { path: 'attendees.internal', select: 'name email' }
      ]);

      return updatedBooking!;
    } catch (error: any) {
      throw HttpException.BadRequest(error.message);
    }
  }

  /**
   * Cancel booking
   */
  async cancelBooking(
    id: string,
    userId: string,
    reason?: string
  ): Promise<IBooking> {
    const booking = await this.getBookingById(id, userId);

    // Only the booking owner can cancel
    if (booking.userId.toString() !== userId) {
      throw HttpException.Forbidden('You can only cancel your own bookings');
    }

    // Don't allow canceling past bookings
    if (DateHelpers.isInPast(booking.startDate)) {
      throw HttpException.BadRequest('Cannot cancel past bookings');
    }

    if (booking.status === 'cancelled') {
      throw HttpException.BadRequest('Booking is already cancelled');
    }

    const updatedBooking = await BookingModel.findByIdAndUpdate(
      id,
      {
        status: 'cancelled',
        cancellationReason: reason,
        cancelledAt: new Date(),
        cancelledBy: userId,
        updatedAt: new Date()
      },
      { new: true }
    ).populate([
      { path: 'resourceId' },
      { path: 'userId', select: 'name email' },
      { path: 'attendees.internal', select: 'name email' }
    ]);

    return updatedBooking!;
  }

  /**
   * Get all bookings (admin only)
   */
  async getAllBookings(options: {
    startDate?: Date;
    endDate?: Date;
    status?: string[];
    resourceType?: 'meeting-room' | 'desk';
    resourceId?: string;
    limit?: number;
    skip?: number;
  } = {}): Promise<IBooking[]> {
    const query: any = {};

    if (options.startDate || options.endDate) {
      query.startDate = {};
      if (options.startDate) query.startDate.$gte = options.startDate;
      if (options.endDate) query.startDate.$lte = options.endDate;
    }

    if (options.status) {
      query.status = { $in: options.status };
    }

    if (options.resourceType) {
      query.resourceType = options.resourceType;
    }

    if (options.resourceId) {
      query.resourceId = options.resourceId;
    }

    return await BookingModel.find(query)
      .populate('resourceId')
      .populate('userId', 'name email')
      .populate('attendees.internal', 'name email')
      .sort({ startDate: -1 })
      .limit(options.limit || 100)
      .skip(options.skip || 0);
  }

  /**
   * Check resource availability
   */
  private async checkResourceAvailability(
    resourceType: 'meeting-room' | 'desk',
    resourceId: string,
    startDate: Date,
    endDate: Date,
    userId?: string,
    excludeBookingId?: string
  ): Promise<{ available: boolean; conflicts?: any[] }> {
    if (resourceType === 'meeting-room') {
      return await this.meetingRoomsService.checkAvailability(resourceId, startDate, endDate);
    } else {
      // For desk bookings, we only need the date (not endDate)
      return await this.desksService.checkAvailability(resourceId, startDate, userId);
    }
  }

  /**
   * Get booking statistics
   */
  async getBookingStats(options: {
    startDate?: Date;
    endDate?: Date;
    userId?: string;
  } = {}): Promise<any> {
    const matchStage: any = {};

    if (options.startDate || options.endDate) {
      matchStage.startDate = {};
      if (options.startDate) matchStage.startDate.$gte = options.startDate;
      if (options.endDate) matchStage.startDate.$lte = options.endDate;
    }

    if (options.userId) {
      matchStage.userId = options.userId;
    }

    const stats = await BookingModel.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: null,
          totalBookings: { $sum: 1 },
          confirmedBookings: {
            $sum: { $cond: [{ $eq: ['$status', 'confirmed'] }, 1, 0] }
          },
          cancelledBookings: {
            $sum: { $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0] }
          },
          meetingRoomBookings: {
            $sum: { $cond: [{ $eq: ['$resourceType', 'meeting-room'] }, 1, 0] }
          },
          deskBookings: {
            $sum: { $cond: [{ $eq: ['$resourceType', 'desk'] }, 1, 0] }
          }
        }
      }
    ]);

    return stats[0] || {
      totalBookings: 0,
      confirmedBookings: 0,
      cancelledBookings: 0,
      meetingRoomBookings: 0,
      deskBookings: 0
    };
  }

  /**
   * Get upcoming bookings for today
   */
  async getTodayBookings(): Promise<IBooking[]> {
    const today = new Date();
    const startOfDay = DateHelpers.getStartOfDay(today);
    const endOfDay = DateHelpers.getEndOfDay(today);

    return await BookingModel.find({
      startDate: { $gte: startOfDay, $lte: endOfDay },
      status: 'confirmed'
    })
      .populate('resourceId')
      .populate('userId', 'name email')
      .sort({ startDate: 1 });
  }
}
