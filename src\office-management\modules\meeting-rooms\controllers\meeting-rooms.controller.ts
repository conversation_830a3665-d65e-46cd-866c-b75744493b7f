import { Request, Response } from 'express';
import { MeetingRoomsService } from '../services/meeting-rooms.service';
import { HttpStatus } from '../../../exceptions/HttpStatus';
import { HttpException } from '../../../exceptions/HttpExceptions';
import { ValidationHelpers } from '../../../utils/validation-helpers';

const meetingRoomsService = new MeetingRoomsService();

/**
 * Get all meeting rooms
 */
export const getAllMeetingRooms = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { isActive, capacity, floor, amenities } = req.query;
    
    const filters: any = {};
    
    if (isActive !== undefined) {
      filters.isActive = isActive === 'true';
    }
    
    if (capacity) {
      filters.capacity = parseInt(capacity as string);
    }
    
    if (floor) {
      filters.floor = floor as string;
    }
    
    if (amenities) {
      filters.amenities = Array.isArray(amenities) ? amenities : [amenities];
    }

    const meetingRooms = await meetingRoomsService.getAllMeetingRooms(filters);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: meetingRooms,
      message: 'Meeting rooms retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting meeting rooms:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to retrieve meeting rooms'
    });
  }
};

/**
 * Get meeting room by ID
 */
export const getMeetingRoomById = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    
    if (!ValidationHelpers.isValidObjectId(id)) {
      throw HttpException.BadRequest('Invalid meeting room ID');
    }

    const meetingRoom = await meetingRoomsService.getMeetingRoomById(id);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: meetingRoom,
      message: 'Meeting room retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting meeting room:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to retrieve meeting room'
    });
  }
};

/**
 * Create new meeting room (Admin only)
 */
export const createMeetingRoom = async (req: Request, res: Response): Promise<Response> => {
  try {
    const requiredFields = ['name', 'location.floor', 'location.room', 'capacity'];
    ValidationHelpers.validateRequiredFields(req.body, requiredFields);

    const meetingRoom = await meetingRoomsService.createMeetingRoom(req.body);

    return res.status(HttpStatus.CREATED).json({
      success: true,
      data: meetingRoom,
      message: 'Meeting room created successfully'
    });
  } catch (error) {
    console.error('Error creating meeting room:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to create meeting room'
    });
  }
};

/**
 * Update meeting room (Admin only)
 */
export const updateMeetingRoom = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    
    if (!ValidationHelpers.isValidObjectId(id)) {
      throw HttpException.BadRequest('Invalid meeting room ID');
    }

    const meetingRoom = await meetingRoomsService.updateMeetingRoom(id, req.body);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: meetingRoom,
      message: 'Meeting room updated successfully'
    });
  } catch (error) {
    console.error('Error updating meeting room:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to update meeting room'
    });
  }
};

/**
 * Delete meeting room (Admin only)
 */
export const deleteMeetingRoom = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    
    if (!ValidationHelpers.isValidObjectId(id)) {
      throw HttpException.BadRequest('Invalid meeting room ID');
    }

    await meetingRoomsService.deleteMeetingRoom(id);

    return res.status(HttpStatus.OK).json({
      success: true,
      message: 'Meeting room deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting meeting room:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to delete meeting room'
    });
  }
};

/**
 * Check meeting room availability
 */
export const checkAvailability = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const { startDate, endDate } = req.body;
    
    if (!ValidationHelpers.isValidObjectId(id)) {
      throw HttpException.BadRequest('Invalid meeting room ID');
    }

    ValidationHelpers.validateRequiredFields(req.body, ['startDate', 'endDate']);

    const start = new Date(startDate);
    const end = new Date(endDate);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      throw HttpException.BadRequest('Invalid date format');
    }

    if (start >= end) {
      throw HttpException.BadRequest('Start date must be before end date');
    }

    const availability = await meetingRoomsService.checkAvailability(id, start, end);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: availability,
      message: 'Availability checked successfully'
    });
  } catch (error) {
    console.error('Error checking availability:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to check availability'
    });
  }
};

/**
 * Get meeting room availability calendar
 */
export const getAvailabilityCalendar = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const { startDate, endDate } = req.query;
    
    if (!ValidationHelpers.isValidObjectId(id)) {
      throw HttpException.BadRequest('Invalid meeting room ID');
    }

    if (!startDate || !endDate) {
      throw HttpException.BadRequest('Start date and end date are required');
    }

    const start = new Date(startDate as string);
    const end = new Date(endDate as string);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      throw HttpException.BadRequest('Invalid date format');
    }

    const calendar = await meetingRoomsService.getAvailabilityCalendar(id, start, end);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: calendar,
      message: 'Calendar retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting calendar:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to retrieve calendar'
    });
  }
};

/**
 * Get meeting rooms with current availability status
 */
export const getMeetingRoomsWithAvailability = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { date } = req.query;
    
    let targetDate: Date | undefined;
    if (date) {
      targetDate = new Date(date as string);
      if (isNaN(targetDate.getTime())) {
        throw HttpException.BadRequest('Invalid date format');
      }
    }

    const rooms = await meetingRoomsService.getMeetingRoomsWithAvailability(targetDate);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: rooms,
      message: 'Meeting rooms with availability retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting rooms with availability:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to retrieve rooms with availability'
    });
  }
};
