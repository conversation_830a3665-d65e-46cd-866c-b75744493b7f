import { AsyncController } from '@/types&interfaces/types';
import PermissionSet from '../models/permissionSetSchema';
import { PermissionMatrix } from '@/constants/permission-matrix';
import { Types } from 'mongoose';
import User from '@/models/userSchema';

export interface PermissionSetDto {
  name?: string;

  role?: string;

  area?: string;

  permissions?: Array<{
    section: string;
    subSection: string;
    capability: string;
  }>;
}

export const getPermissionMatrix: AsyncController = async (req, res) => {
  try {
    return res.status(200).json({
      message: 'Permission Matrix fetched successfully',
      permissionMatrix: PermissionMatrix, // Return the matrix directly
    });
  } catch (error: any) {
    return res.status(500).json({
      message: 'Failed to fetch permission matrix',
      error: error.message,
    });
  }
};

export const getAllPermissionSets: AsyncController = async (req, res) => {
  try {
    const permissionSets = await PermissionSet.find();
    return res
      .status(200)
      .send({ message: 'Permission sets fetched successfully', permissionSets: permissionSets });
  } catch (error: any) {
    return res.status(500).json({ message: 'Error fetching permission sets', error: error.message });
  }
};

export const getPermissionSetById: AsyncController = async (req, res) => {
  const { id } = req.params;

  if (!Types.ObjectId.isValid(id)) {
    return res.status(400).json({ message: 'Invalid permission set ID format' });
  }

  try {
    const permissionSet = await PermissionSet.findById(id);
    if (!permissionSet) {
      return res.status(404).json({ message: 'Permission set not found' });
    }
    return res.status(200).send({ message: 'Permission set found', permissionSet: permissionSet });
  } catch (error: any) {
    return res.status(500).json({ message: 'Error fetching permission set', error: error.message });
  }
};

export const addPermissionSet: AsyncController = async (req, res) => {
  try {
    const createPermissionSetDto: PermissionSetDto = req.body;
    const currentPermissionSet = await PermissionSet.findOne({
      role: createPermissionSetDto.role,
      area: createPermissionSetDto.area,
    });

    if (currentPermissionSet) {
      return res.status(409).send({
        message: `El conjunto de permisos con esta área y rol ya existe.`,
      });
    }

    const newPermissionSet = new PermissionSet(createPermissionSetDto);
    await newPermissionSet.save();
    return res.status(201).send({ message: 'Permission set created successfully', data: newPermissionSet });
  } catch (error: any) {
    return res.status(500).json({ message: 'Error creating permission set', error: error.message });
  }
};

export const editPermissionSet: AsyncController = async (req, res) => {
  try {
    const { id } = req.params;
    const updatePermissionSetDto: PermissionSetDto = req.body;

    if (!Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'Invalid permission set ID format' });
    }

    const currentPermissionSet = await PermissionSet.findOne({
      _id: { $ne: id },
      role: updatePermissionSetDto.role,
      area: updatePermissionSetDto.area,
    });

    if (currentPermissionSet) {
      return res.status(409).send({
        message: `El conjunto de permisos con esta área y rol ya existe.`,
      });
    }
    const updatedPermissionSet = await PermissionSet.findByIdAndUpdate(
      id,
      {
        $set: {
          name: updatePermissionSetDto.name,
          role: updatePermissionSetDto.role,
          area: updatePermissionSetDto.area,
          permissions: updatePermissionSetDto.permissions,
        },
      },
      { new: true }
    );
    if (!updatedPermissionSet) {
      return res.status(404).json({ message: 'Permission set not found' });
    }
    return res
      .status(200)
      .send({ message: 'Permission set updated successfully', data: updatedPermissionSet });
  } catch (error: any) {
    return res.status(500).json({ message: 'Error updating permission set', error: error.message });
  }
};

export const assignPermissionSet: AsyncController = async (req, res) => {
  const { id } = req.params;
  const assignPermissionSetDto: PermissionSetDto = req.body;

  if (!Types.ObjectId.isValid(id)) {
    return res.status(400).json({ message: 'Invalid user ID format' });
  }

  try {
    const assignPermissionSetUser = await User.findByIdAndUpdate(
      id,
      {
        $set: {
          permissions: assignPermissionSetDto.permissions,
        },
      },
      { new: true }
    );
    if (!assignPermissionSetUser) {
      return res.status(404).json({ message: 'User not found' });
    }
    return res
      .status(200)
      .send({ message: 'Permission set updated successfully', data: assignPermissionSetUser });
  } catch (error: any) {
    return res.status(500).json({ message: 'Error updating permission set', error: error.message });
  }
};

export const deletePermissionSet: AsyncController = async (req, res) => {
  const { id } = req.params;

  if (!Types.ObjectId.isValid(id)) {
    return res.status(400).json({ message: 'Invalid permission set ID format' });
  }

  try {
    const permissionSet = await PermissionSet.findById(id);
    if (!permissionSet) {
      return res.status(404).json({ message: 'Permission set not found' });
    }

    const users = await User.find({
      area: permissionSet.area,
      role: permissionSet.role,
    });

    if (users && users.length > 0) {
      return res.status(400).json({
        message: 'No se puede eliminar este rol de acceso porque hay usuarios asignados a él actualmente.',
      });
    }

    const deletedPermissionSet = await PermissionSet.findByIdAndDelete(id);
    if (!deletedPermissionSet) {
      return res.status(404).json({ message: 'Permission set not found' });
    }
    return res.status(200).send({ message: 'Permission set deleted successfully' });
  } catch (error: any) {
    return res.status(500).json({ message: 'Error deleting permission set', error: error.message });
  }
};
