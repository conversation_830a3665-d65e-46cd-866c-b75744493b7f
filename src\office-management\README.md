# Office Management Platform

Internal platform for managing the reservation of meeting rooms, desks, and visitor invitations at OneCarNow offices.

## Features

- **Space Management**: Manage meeting rooms and desks
- **User Management**: Google SSO with @onecarnow domain validation
- **Meeting Room Booking**: Reserve rooms by hour with Slack confirmations
- **Desk Booking**: Book desks by full day
- **Guest Invitation System**: Invite guests with email/WhatsApp confirmations
- **Front Desk Dashboard**: Manage daily guest lists and check-ins

## Structure

```
office-management/
├── constants/           # Platform constants and configurations
├── db/                 # Database connection
├── exceptions/         # Custom exception classes
├── middlewares/        # Authentication and validation middlewares
├── modules/           # Feature modules
│   ├── auth/          # Authentication module
│   ├── meeting-rooms/ # Meeting rooms management
│   ├── desks/         # Desk management
│   ├── bookings/      # Booking system
│   ├── guests/        # Guest management
│   └── users/         # User management
├── services/          # Shared services (notifications, etc.)
├── utils/             # Utility functions
├── general-routes.ts  # General platform routes
└── index.routes.ts    # Main router configuration
```

## API Endpoints

All endpoints are prefixed with `/office-management`

### Authentication
- `POST /auth/google-login` - Google SSO login
- `POST /auth/logout` - Logout

### Meeting Rooms
- `GET /meeting-rooms` - List all meeting rooms
- `POST /meeting-rooms` - Create meeting room (admin)
- `GET /meeting-rooms/:id/availability` - Check availability
- `POST /meeting-rooms/:id/book` - Book meeting room

### Desks
- `GET /desks` - List all desks
- `POST /desks` - Create desk (admin)
- `GET /desks/availability` - Check desk availability
- `POST /desks/:id/book` - Book desk

### Bookings
- `GET /bookings` - List user bookings
- `GET /bookings/:id` - Get booking details
- `PATCH /bookings/:id/cancel` - Cancel booking

### Guests
- `POST /guests/invite` - Invite guest
- `GET /guests/daily` - Daily guest list (receptionist)
- `PATCH /guests/:id/check-in` - Check in guest
- `PATCH /guests/:id/check-out` - Check out guest

## Environment Variables

```env
# Office Management
OFFICE_MANAGEMENT_ACCESS_TOKEN_SECRET=your-secret
OFFICE_MANAGEMENT_FROM_EMAIL=<EMAIL>

# Google SSO
GOOGLE_CLIENT_ID=your-client-id
GOOGLE_CLIENT_SECRET=your-client-secret
GOOGLE_REDIRECT_URI=your-redirect-uri

# Slack Integration
SLACK_WEBHOOK_URL=your-webhook-url
SLACK_OFFICE_CHANNEL=#office-bookings

# WhatsApp Integration
WHATSAPP_API_URL=your-api-url
WHATSAPP_API_KEY=your-api-key
WHATSAPP_PHONE_NUMBER=your-phone-number
```

## User Roles

- **Admin**: Full access to all features and management
- **Employee**: Can book rooms and desks, invite guests
- **Receptionist**: Can view and manage guest lists, check-ins/check-outs

## Business Rules

- Only @onecarnow email addresses are allowed
- Bookings are only allowed during business hours (8:00 AM - 6:00 PM)
- No bookings on weekends
- Meeting rooms can be booked by hour (1-8 hours)
- Desks can be booked for full day only
- One desk per person per day
- Bookings can be made up to 30 days in advance
- Automatic Slack confirmations for internal users
- Email/WhatsApp confirmations for guests
