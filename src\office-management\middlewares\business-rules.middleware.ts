import { Request, Response, NextFunction } from 'express';
import { BusinessRulesService } from '../services/business-rules.service';

const businessRulesService = new BusinessRulesService();

// Middleware to validate booking business rules
export const validateBookingRules = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const validation = await businessRulesService.validateBooking(req.body, userId);
    
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        message: validation.message,
        details: validation.details
      });
    }

    next();
  } catch (error) {
    console.error('Error in booking validation middleware:', error);
    res.status(500).json({
      success: false,
      message: 'Error validating booking rules'
    });
  }
};

// Middleware to validate guest business rules
export const validateGuestRules = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const validation = await businessRulesService.validateGuestVisit(req.body, userId);
    
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        message: validation.message,
        details: validation.details
      });
    }

    next();
  } catch (error) {
    console.error('Error in guest validation middleware:', error);
    res.status(500).json({
      success: false,
      message: 'Error validating guest rules'
    });
  }
};

// Middleware to validate email domain
export const validateEmailDomain = (req: Request, res: Response, next: NextFunction) => {
  try {
    const { email } = req.body;
    
    if (email) {
      const validation = businessRulesService.validateEmailDomain(email);
      
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          message: validation.message
        });
      }
    }

    next();
  } catch (error) {
    console.error('Error in email domain validation middleware:', error);
    res.status(500).json({
      success: false,
      message: 'Error validating email domain'
    });
  }
};

// Middleware to check admin permissions
export const requireAdmin = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    // Import here to avoid circular dependency
    const { OfficeUserModel } = await import('../models/user.model');
    const user = await OfficeUserModel.findById(userId);
    
    if (!user || !user.permissions.isAdmin) {
      return res.status(403).json({
        success: false,
        message: 'Admin permissions required'
      });
    }

    next();
  } catch (error) {
    console.error('Error in admin permission middleware:', error);
    res.status(500).json({
      success: false,
      message: 'Error checking admin permissions'
    });
  }
};

// Middleware to check guest management permissions
export const requireGuestManagement = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    // Import here to avoid circular dependency
    const { OfficeUserModel } = await import('../models/user.model');
    const user = await OfficeUserModel.findById(userId);
    
    if (!user || (!user.permissions.canManageGuests && !user.permissions.isAdmin)) {
      return res.status(403).json({
        success: false,
        message: 'Guest management permissions required'
      });
    }

    next();
  } catch (error) {
    console.error('Error in guest management permission middleware:', error);
    res.status(500).json({
      success: false,
      message: 'Error checking guest management permissions'
    });
  }
};

// Middleware to check booking cancellation permissions
export const validateCancellationRules = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.id;
    const bookingId = req.params.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const validation = await businessRulesService.canCancelBooking(bookingId, userId);
    
    if (!validation.isValid) {
      return res.status(403).json({
        success: false,
        message: validation.message
      });
    }

    next();
  } catch (error) {
    console.error('Error in cancellation validation middleware:', error);
    res.status(500).json({
      success: false,
      message: 'Error validating cancellation rules'
    });
  }
};

// Middleware to check working hours for operations
export const checkWorkingHours = (req: Request, res: Response, next: NextFunction) => {
  try {
    const now = new Date();
    const currentHour = now.getHours();
    const currentDay = now.getDay();
    
    // Allow operations Monday-Friday, 6 AM - 10 PM
    const isWorkingDay = currentDay >= 1 && currentDay <= 5;
    const isWorkingHour = currentHour >= 6 && currentHour <= 22;
    
    if (!isWorkingDay || !isWorkingHour) {
      return res.status(400).json({
        success: false,
        message: 'This operation is only available during working hours (Monday-Friday, 6 AM - 10 PM)'
      });
    }

    next();
  } catch (error) {
    console.error('Error in working hours middleware:', error);
    res.status(500).json({
      success: false,
      message: 'Error checking working hours'
    });
  }
};

// Middleware to rate limit booking operations per user
const userBookingAttempts = new Map<string, { count: number; resetTime: number }>();

export const rateLimitBookings = (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const now = Date.now();
    const windowMs = 60 * 1000; // 1 minute window
    const maxAttempts = 10; // Max 10 booking operations per minute

    const userAttempts = userBookingAttempts.get(userId);
    
    if (!userAttempts || now > userAttempts.resetTime) {
      // Reset or initialize counter
      userBookingAttempts.set(userId, {
        count: 1,
        resetTime: now + windowMs
      });
      next();
      return;
    }

    if (userAttempts.count >= maxAttempts) {
      return res.status(429).json({
        success: false,
        message: 'Too many booking operations. Please try again later.'
      });
    }

    // Increment counter
    userAttempts.count++;
    userBookingAttempts.set(userId, userAttempts);
    
    next();
  } catch (error) {
    console.error('Error in rate limiting middleware:', error);
    res.status(500).json({
      success: false,
      message: 'Error in rate limiting'
    });
  }
};

// Cleanup rate limiting data periodically
setInterval(() => {
  const now = Date.now();
  for (const [userId, attempts] of userBookingAttempts.entries()) {
    if (now > attempts.resetTime) {
      userBookingAttempts.delete(userId);
    }
  }
}, 5 * 60 * 1000); // Cleanup every 5 minutes
