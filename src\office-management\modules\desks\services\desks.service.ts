import { DeskModel, IDesk, BookingModel } from '../../../models';
import { HttpException } from '../../../exceptions/HttpExceptions';
import { HttpStatus } from '../../../exceptions/HttpStatus';
import { DateHelpers } from '../../../utils/date-helpers';

export class DesksService {
  /**
   * Get all desks with optional filters
   */
  async getAllDesks(filters: {
    isActive?: boolean;
    type?: string;
    floor?: string;
    zone?: string;
    amenities?: string[];
  } = {}) {
    const query: any = {};
    
    if (filters.isActive !== undefined) {
      query.isActive = filters.isActive;
    }
    
    if (filters.type) {
      query.type = filters.type;
    }
    
    if (filters.floor) {
      query['location.floor'] = filters.floor;
    }
    
    if (filters.zone) {
      query['location.zone'] = filters.zone;
    }
    
    if (filters.amenities && filters.amenities.length > 0) {
      query.amenities = { $in: filters.amenities };
    }

    return await DeskModel.find(query).sort({ name: 1 });
  }

  /**
   * Get desk by ID
   */
  async getDeskById(id: string): Promise<IDesk> {
    const desk = await DeskModel.findById(id);
    
    if (!desk) {
      throw HttpException.NotFound('Desk not found');
    }
    
    return desk;
  }

  /**
   * Create new desk
   */
  async createDesk(data: Partial<IDesk>): Promise<IDesk> {
    try {
      const desk = new DeskModel(data);
      return await desk.save();
    } catch (error: any) {
      if (error.code === 11000) {
        throw HttpException.BadRequest('Desk with this name already exists');
      }
      throw HttpException.BadRequest(error.message);
    }
  }

  /**
   * Update desk
   */
  async updateDesk(id: string, data: Partial<IDesk>): Promise<IDesk> {
    try {
      const desk = await DeskModel.findByIdAndUpdate(
        id,
        { ...data, updatedAt: new Date() },
        { new: true, runValidators: true }
      );
      
      if (!desk) {
        throw HttpException.NotFound('Desk not found');
      }
      
      return desk;
    } catch (error: any) {
      if (error.code === 11000) {
        throw HttpException.BadRequest('Desk with this name already exists');
      }
      throw HttpException.BadRequest(error.message);
    }
  }

  /**
   * Delete desk
   */
  async deleteDesk(id: string): Promise<void> {
    // Check if there are any future bookings
    const futureBookings = await BookingModel.countDocuments({
      resourceType: 'desk',
      resourceId: id,
      startDate: { $gte: new Date() },
      status: { $in: ['confirmed'] }
    });

    if (futureBookings > 0) {
      throw HttpException.BadRequest('Cannot delete desk with future bookings');
    }

    const result = await DeskModel.findByIdAndDelete(id);
    
    if (!result) {
      throw HttpException.NotFound('Desk not found');
    }
  }

  /**
   * Check availability for a desk (full day booking)
   */
  async checkAvailability(
    deskId: string,
    date: Date,
    userId?: string
  ): Promise<{ available: boolean; conflicts?: any[] }> {
    const desk = await this.getDeskById(deskId);
    
    if (!desk.isActive) {
      return { available: false, conflicts: ['Desk is not active'] };
    }

    // Check if it's a working day
    const dayOfWeek = date.getDay();
    if (!desk.availability.workingDays.includes(dayOfWeek)) {
      return { available: false, conflicts: ['Not a working day'] };
    }

    // Check advance booking rules
    const now = new Date();
    const hoursUntilDate = (date.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    if (hoursUntilDate < desk.bookingRules.minNoticeHours) {
      return { 
        available: false, 
        conflicts: [`Minimum ${desk.bookingRules.minNoticeHours} hours notice required`] 
      };
    }

    const daysUntilDate = hoursUntilDate / 24;
    if (daysUntilDate > desk.bookingRules.advanceBookingDays) {
      return { 
        available: false, 
        conflicts: [`Cannot book more than ${desk.bookingRules.advanceBookingDays} days in advance`] 
      };
    }

    // Check for conflicting bookings (full day)
    const startOfDay = DateHelpers.getStartOfDay(date);
    const endOfDay = DateHelpers.getEndOfDay(date);

    const conflicts = await BookingModel.find({
      resourceType: 'desk',
      resourceId: deskId,
      status: { $in: ['confirmed'] },
      startDate: { $lt: endOfDay },
      endDate: { $gt: startOfDay }
    }).populate('userId', 'name email');

    if (conflicts.length > 0) {
      return { available: false, conflicts };
    }

    // Check if user already has a desk booking for this date
    if (userId) {
      const userBooking = await BookingModel.findOne({
        resourceType: 'desk',
        userId: userId,
        status: { $in: ['confirmed'] },
        startDate: { $lt: endOfDay },
        endDate: { $gt: startOfDay }
      });

      if (userBooking) {
        return { 
          available: false, 
          conflicts: ['You already have a desk booking for this date'] 
        };
      }
    }

    return { available: true };
  }

  /**
   * Get desk availability for a date range
   */
  async getAvailabilityCalendar(
    deskId: string,
    startDate: Date,
    endDate: Date
  ): Promise<any[]> {
    const bookings = await BookingModel.find({
      resourceType: 'desk',
      resourceId: deskId,
      status: { $in: ['confirmed'] },
      startDate: { $lt: endDate },
      endDate: { $gt: startDate }
    }).populate('userId', 'name email').sort({ startDate: 1 });

    return bookings.map(booking => ({
      id: booking._id,
      title: `${booking.title} - Full Day`,
      start: booking.startDate,
      end: booking.endDate,
      user: booking.userId,
      status: booking.status,
      allDay: true
    }));
  }

  /**
   * Get desks with their current availability status
   */
  async getDesksWithAvailability(date?: Date): Promise<any[]> {
    const targetDate = date || new Date();
    const startOfDay = DateHelpers.getStartOfDay(targetDate);
    const endOfDay = DateHelpers.getEndOfDay(targetDate);

    const desks = await DeskModel.find({ isActive: true }).sort({ name: 1 });
    
    const desksWithAvailability = await Promise.all(
      desks.map(async (desk) => {
        const booking = await BookingModel.findOne({
          resourceType: 'desk',
          resourceId: desk._id,
          status: { $in: ['confirmed'] },
          startDate: { $lt: endOfDay },
          endDate: { $gt: startOfDay }
        }).populate('userId', 'name email');

        const currentStatus = booking ? 'occupied' : 'available';

        return {
          ...desk.toJSON(),
          currentStatus,
          currentBooking: booking,
          isBookedToday: !!booking
        };
      })
    );

    return desksWithAvailability;
  }

  /**
   * Get available desks for a specific date
   */
  async getAvailableDesks(date: Date, filters: any = {}): Promise<IDesk[]> {
    const startOfDay = DateHelpers.getStartOfDay(date);
    const endOfDay = DateHelpers.getEndOfDay(date);

    // Get all desks that match filters
    const query: any = { isActive: true, ...filters };
    const allDesks = await DeskModel.find(query);

    // Get booked desk IDs for the date
    const bookedDeskIds = await BookingModel.distinct('resourceId', {
      resourceType: 'desk',
      status: { $in: ['confirmed'] },
      startDate: { $lt: endOfDay },
      endDate: { $gt: startOfDay }
    });

    // Filter out booked desks
    return allDesks.filter(desk => !bookedDeskIds.includes(desk._id));
  }

  /**
   * Get user's desk booking history
   */
  async getUserDeskBookings(
    userId: string,
    options: {
      startDate?: Date;
      endDate?: Date;
      status?: string[];
      limit?: number;
      skip?: number;
    } = {}
  ): Promise<any[]> {
    const query: any = {
      resourceType: 'desk',
      userId: userId
    };

    if (options.startDate || options.endDate) {
      query.startDate = {};
      if (options.startDate) query.startDate.$gte = options.startDate;
      if (options.endDate) query.startDate.$lte = options.endDate;
    }

    if (options.status) {
      query.status = { $in: options.status };
    }

    return await BookingModel.find(query)
      .populate('resourceId', 'name location type')
      .sort({ startDate: -1 })
      .limit(options.limit || 50)
      .skip(options.skip || 0);
  }
}
