import { Request, Response } from 'express';
import { DesksService } from '../services/desks.service';
import { HttpStatus } from '../../../exceptions/HttpStatus';
import { HttpException } from '../../../exceptions/HttpExceptions';
import { ValidationHelpers } from '../../../utils/validation-helpers';

const desksService = new DesksService();

/**
 * Get all desks
 */
export const getAllDesks = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { isActive, type, floor, zone, amenities } = req.query;
    
    const filters: any = {};
    
    if (isActive !== undefined) {
      filters.isActive = isActive === 'true';
    }
    
    if (type) {
      filters.type = type as string;
    }
    
    if (floor) {
      filters.floor = floor as string;
    }
    
    if (zone) {
      filters.zone = zone as string;
    }
    
    if (amenities) {
      filters.amenities = Array.isArray(amenities) ? amenities : [amenities];
    }

    const desks = await desksService.getAllDesks(filters);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: desks,
      message: 'Desks retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting desks:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to retrieve desks'
    });
  }
};

/**
 * Get desk by ID
 */
export const getDeskById = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    
    if (!ValidationHelpers.isValidObjectId(id)) {
      throw HttpException.BadRequest('Invalid desk ID');
    }

    const desk = await desksService.getDeskById(id);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: desk,
      message: 'Desk retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting desk:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to retrieve desk'
    });
  }
};

/**
 * Create new desk (Admin only)
 */
export const createDesk = async (req: Request, res: Response): Promise<Response> => {
  try {
    const requiredFields = ['name', 'location.floor', 'location.zone', 'type'];
    ValidationHelpers.validateRequiredFields(req.body, requiredFields);

    const desk = await desksService.createDesk(req.body);

    return res.status(HttpStatus.CREATED).json({
      success: true,
      data: desk,
      message: 'Desk created successfully'
    });
  } catch (error) {
    console.error('Error creating desk:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to create desk'
    });
  }
};

/**
 * Update desk (Admin only)
 */
export const updateDesk = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    
    if (!ValidationHelpers.isValidObjectId(id)) {
      throw HttpException.BadRequest('Invalid desk ID');
    }

    const desk = await desksService.updateDesk(id, req.body);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: desk,
      message: 'Desk updated successfully'
    });
  } catch (error) {
    console.error('Error updating desk:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to update desk'
    });
  }
};

/**
 * Delete desk (Admin only)
 */
export const deleteDesk = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    
    if (!ValidationHelpers.isValidObjectId(id)) {
      throw HttpException.BadRequest('Invalid desk ID');
    }

    await desksService.deleteDesk(id);

    return res.status(HttpStatus.OK).json({
      success: true,
      message: 'Desk deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting desk:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to delete desk'
    });
  }
};

/**
 * Check desk availability for a specific date
 */
export const checkAvailability = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const { date } = req.body;
    const userId = req.userOfficeManagement?.userId;
    
    if (!ValidationHelpers.isValidObjectId(id)) {
      throw HttpException.BadRequest('Invalid desk ID');
    }

    ValidationHelpers.validateRequiredFields(req.body, ['date']);

    const targetDate = new Date(date);

    if (isNaN(targetDate.getTime())) {
      throw HttpException.BadRequest('Invalid date format');
    }

    const availability = await desksService.checkAvailability(id, targetDate, userId);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: availability,
      message: 'Availability checked successfully'
    });
  } catch (error) {
    console.error('Error checking availability:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to check availability'
    });
  }
};

/**
 * Get desk availability calendar
 */
export const getAvailabilityCalendar = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const { startDate, endDate } = req.query;
    
    if (!ValidationHelpers.isValidObjectId(id)) {
      throw HttpException.BadRequest('Invalid desk ID');
    }

    if (!startDate || !endDate) {
      throw HttpException.BadRequest('Start date and end date are required');
    }

    const start = new Date(startDate as string);
    const end = new Date(endDate as string);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      throw HttpException.BadRequest('Invalid date format');
    }

    const calendar = await desksService.getAvailabilityCalendar(id, start, end);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: calendar,
      message: 'Calendar retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting calendar:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to retrieve calendar'
    });
  }
};

/**
 * Get desks with current availability status
 */
export const getDesksWithAvailability = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { date } = req.query;
    
    let targetDate: Date | undefined;
    if (date) {
      targetDate = new Date(date as string);
      if (isNaN(targetDate.getTime())) {
        throw HttpException.BadRequest('Invalid date format');
      }
    }

    const desks = await desksService.getDesksWithAvailability(targetDate);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: desks,
      message: 'Desks with availability retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting desks with availability:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to retrieve desks with availability'
    });
  }
};

/**
 * Get available desks for a specific date
 */
export const getAvailableDesks = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { date, type, floor, zone } = req.query;
    
    if (!date) {
      throw HttpException.BadRequest('Date is required');
    }

    const targetDate = new Date(date as string);
    if (isNaN(targetDate.getTime())) {
      throw HttpException.BadRequest('Invalid date format');
    }

    const filters: any = {};
    if (type) filters.type = type;
    if (floor) filters['location.floor'] = floor;
    if (zone) filters['location.zone'] = zone;

    const availableDesks = await desksService.getAvailableDesks(targetDate, filters);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: availableDesks,
      message: 'Available desks retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting available desks:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to retrieve available desks'
    });
  }
};
