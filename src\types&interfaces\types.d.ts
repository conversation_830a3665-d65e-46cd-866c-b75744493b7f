/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { Response, Request, NextFunction } from 'express';
import { MyRequest } from './interfaces';
import jwt from 'jsonwebtoken';

type AsyncController = (
  req: MyRequest,
  res: Response,
  next?: NextFunction
) => Promise<Response<any, Record<string, any>>>;

type Controller = (
  req: MyRequest,
  res: Response,
  next: NextFunction
) => Response<any, Record<string, any>> | void;

type MiddlewareController = (
  req: MyRequest,
  res: Response,
  next: NextFunction
) => Promise<Response<any, Record<string, any>> | void>;

export type AuthUser = {
  userId: string;
  iat: number;
  exp: number;
  role?: string;
  email?: string;
};

type OfficeManagementUser = {
  userId: string;
  email: string;
  role: string;
  iat: number;
  exp: number;
  isAuthenticated: boolean;
};

declare global {
  namespace Express {
    export interface Request {
      authUser: AuthUser;
      userOfficeManagement?: OfficeManagementUser;
    }
  }
}
