import { body, query, param } from 'express-validator';

export class MeetingRoomValidator {
  static createMeetingRoom = [
    body('name')
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('Name must be between 1 and 100 characters')
      .custom(async (value) => {
        // Check for unique name will be handled by MongoDB unique constraint
        return true;
      }),
    
    body('location.building')
      .optional()
      .trim()
      .isLength({ max: 100 })
      .withMessage('Building name must not exceed 100 characters'),
    
    body('location.floor')
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('Floor must be between 1 and 50 characters'),
    
    body('location.room')
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('Room must be between 1 and 50 characters'),
    
    body('location.address')
      .optional()
      .trim()
      .isLength({ max: 200 })
      .withMessage('Address must not exceed 200 characters'),
    
    body('capacity')
      .isInt({ min: 1, max: 100 })
      .withMessage('Capacity must be between 1 and 100 people'),
    
    body('amenities')
      .optional()
      .isArray()
      .withMessage('Amenities must be an array')
      .custom((value) => {
        if (value && value.length > 0) {
          const validAmenities = [
            'projector', 'whiteboard', 'video_conference', 'coffee_machine',
            'smart_tv', 'sound_system', 'air_conditioning', 'natural_light',
            'phone', 'flip_chart', 'markers', 'wifi'
          ];
          const invalidAmenities = value.filter((amenity: string) => !validAmenities.includes(amenity));
          if (invalidAmenities.length > 0) {
            throw new Error(`Invalid amenities: ${invalidAmenities.join(', ')}`);
          }
        }
        return true;
      }),
    
    body('equipment')
      .optional()
      .isArray()
      .withMessage('Equipment must be an array'),
    
    body('description')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Description must not exceed 500 characters'),
    
    body('images')
      .optional()
      .isArray()
      .withMessage('Images must be an array')
      .custom((value) => {
        if (value && value.length > 0) {
          const urlRegex = /^https?:\/\/.+\.(jpg|jpeg|png|gif)$/i;
          const invalidUrls = value.filter((url: string) => !urlRegex.test(url));
          if (invalidUrls.length > 0) {
            throw new Error('All image URLs must be valid and end with jpg, jpeg, png, or gif');
          }
          if (value.length > 10) {
            throw new Error('Maximum 10 images allowed');
          }
        }
        return true;
      }),
    
    body('bookingRules.minBookingDuration')
      .optional()
      .isInt({ min: 30 })
      .withMessage('Minimum booking duration must be at least 30 minutes'),
    
    body('bookingRules.maxBookingDuration')
      .optional()
      .isInt({ min: 30, max: 480 })
      .withMessage('Maximum booking duration must be between 30 and 480 minutes'),
    
    body('bookingRules.advanceBookingDays')
      .optional()
      .isInt({ min: 1, max: 90 })
      .withMessage('Advance booking days must be between 1 and 90'),
    
    body('bookingRules.minNoticeHours')
      .optional()
      .isInt({ min: 0, max: 24 })
      .withMessage('Minimum notice hours must be between 0 and 24'),
    
    body('availability.businessHours.start')
      .optional()
      .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .withMessage('Business hours start time must be in HH:MM format'),
    
    body('availability.businessHours.end')
      .optional()
      .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .withMessage('Business hours end time must be in HH:MM format')
      .custom((value, { req }) => {
        if (value && req.body.availability?.businessHours?.start) {
          const startTime = req.body.availability.businessHours.start;
          const [startHour, startMin] = startTime.split(':').map(Number);
          const [endHour, endMin] = value.split(':').map(Number);
          
          const startMinutes = startHour * 60 + startMin;
          const endMinutes = endHour * 60 + endMin;
          
          if (endMinutes <= startMinutes) {
            throw new Error('Business hours end time must be after start time');
          }
        }
        return true;
      }),
    
    body('availability.workingDays')
      .optional()
      .isArray()
      .withMessage('Working days must be an array')
      .custom((value) => {
        if (value && value.length > 0) {
          const validDays = value.every((day: number) => Number.isInteger(day) && day >= 0 && day <= 6);
          if (!validDays) {
            throw new Error('Working days must be integers between 0 (Sunday) and 6 (Saturday)');
          }
          if (value.length === 0) {
            throw new Error('At least one working day must be specified');
          }
        }
        return true;
      }),
    
    body('availability.timezone')
      .optional()
      .isLength({ min: 1 })
      .withMessage('Timezone cannot be empty')
  ];

  static updateMeetingRoom = [
    param('id')
      .isMongoId()
      .withMessage('Meeting room ID must be a valid MongoDB ObjectId'),
    
    body('name')
      .optional()
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('Name must be between 1 and 100 characters'),
    
    body('capacity')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Capacity must be between 1 and 100 people'),
    
    body('amenities')
      .optional()
      .isArray()
      .withMessage('Amenities must be an array'),
    
    body('description')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Description must not exceed 500 characters')
  ];

  static getMeetingRooms = [
    query('capacity')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Capacity filter must be a positive integer'),
    
    query('floor')
      .optional()
      .trim()
      .isLength({ min: 1 })
      .withMessage('Floor filter cannot be empty'),
    
    query('amenities')
      .optional()
      .custom((value) => {
        if (value) {
          const amenities = Array.isArray(value) ? value : value.split(',');
          const validAmenities = [
            'projector', 'whiteboard', 'video_conference', 'coffee_machine',
            'smart_tv', 'sound_system', 'air_conditioning', 'natural_light',
            'phone', 'flip_chart', 'markers', 'wifi'
          ];
          const invalidAmenities = amenities.filter((amenity: string) => !validAmenities.includes(amenity.trim()));
          if (invalidAmenities.length > 0) {
            throw new Error(`Invalid amenities: ${invalidAmenities.join(', ')}`);
          }
        }
        return true;
      }),
    
    query('isActive')
      .optional()
      .isBoolean()
      .withMessage('Is active filter must be a boolean'),
    
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
  ];

  static getAvailableRooms = [
    query('startDate')
      .isISO8601()
      .withMessage('Start date must be a valid ISO 8601 date')
      .custom((value) => {
        const startDate = new Date(value);
        const now = new Date();
        if (startDate < now) {
          throw new Error('Start date cannot be in the past');
        }
        return true;
      }),
    
    query('endDate')
      .isISO8601()
      .withMessage('End date must be a valid ISO 8601 date')
      .custom((value, { req }) => {
        const startDate = new Date(req.query.startDate as string);
        const endDate = new Date(value);
        if (endDate <= startDate) {
          throw new Error('End date must be after start date');
        }
        
        // Check if booking duration is reasonable (max 8 hours)
        const durationHours = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60);
        if (durationHours > 8) {
          throw new Error('Booking duration cannot exceed 8 hours');
        }
        
        return true;
      }),
    
    query('capacity')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Capacity must be a positive integer'),
    
    query('amenities')
      .optional()
      .custom((value) => {
        if (value) {
          const amenities = Array.isArray(value) ? value : value.split(',');
          const validAmenities = [
            'projector', 'whiteboard', 'video_conference', 'coffee_machine',
            'smart_tv', 'sound_system', 'air_conditioning', 'natural_light',
            'phone', 'flip_chart', 'markers', 'wifi'
          ];
          const invalidAmenities = amenities.filter((amenity: string) => !validAmenities.includes(amenity.trim()));
          if (invalidAmenities.length > 0) {
            throw new Error(`Invalid amenities: ${invalidAmenities.join(', ')}`);
          }
        }
        return true;
      })
  ];

  static getMeetingRoomById = [
    param('id')
      .isMongoId()
      .withMessage('Meeting room ID must be a valid MongoDB ObjectId')
  ];

  static getRoomSchedule = [
    param('id')
      .isMongoId()
      .withMessage('Meeting room ID must be a valid MongoDB ObjectId'),
    
    query('startDate')
      .isISO8601()
      .withMessage('Start date must be a valid ISO 8601 date'),
    
    query('endDate')
      .isISO8601()
      .withMessage('End date must be a valid ISO 8601 date')
      .custom((value, { req }) => {
        const startDate = new Date(req.query.startDate as string);
        const endDate = new Date(value);
        if (endDate <= startDate) {
          throw new Error('End date must be after start date');
        }
        
        // Limit schedule query to maximum 30 days
        const daysDiff = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
        if (daysDiff > 30) {
          throw new Error('Schedule query cannot exceed 30 days');
        }
        
        return true;
      })
  ];

  static checkRecurringAvailability = [
    body('roomId')
      .isMongoId()
      .withMessage('Room ID must be a valid MongoDB ObjectId'),
    
    body('startTime')
      .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .withMessage('Start time must be in HH:MM format'),
    
    body('endTime')
      .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .withMessage('End time must be in HH:MM format')
      .custom((value, { req }) => {
        const startTime = req.body.startTime;
        const [startHour, startMin] = startTime.split(':').map(Number);
        const [endHour, endMin] = value.split(':').map(Number);
        
        const startMinutes = startHour * 60 + startMin;
        const endMinutes = endHour * 60 + endMin;
        
        if (endMinutes <= startMinutes) {
          throw new Error('End time must be after start time');
        }
        
        return true;
      }),
    
    body('recurrence.type')
      .isIn(['daily', 'weekly', 'monthly'])
      .withMessage('Recurrence type must be daily, weekly, or monthly'),
    
    body('recurrence.startDate')
      .isISO8601()
      .withMessage('Recurrence start date must be a valid ISO 8601 date'),
    
    body('recurrence.endDate')
      .isISO8601()
      .withMessage('Recurrence end date must be a valid ISO 8601 date')
      .custom((value, { req }) => {
        const startDate = new Date(req.body.recurrence.startDate);
        const endDate = new Date(value);
        if (endDate <= startDate) {
          throw new Error('Recurrence end date must be after start date');
        }
        
        // Limit recurring bookings to maximum 6 months
        const monthsDiff = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 30);
        if (monthsDiff > 6) {
          throw new Error('Recurring bookings cannot exceed 6 months');
        }
        
        return true;
      }),
    
    body('recurrence.daysOfWeek')
      .optional()
      .isArray()
      .withMessage('Days of week must be an array')
      .custom((value) => {
        if (value && value.length > 0) {
          const validDays = value.every((day: number) => Number.isInteger(day) && day >= 0 && day <= 6);
          if (!validDays) {
            throw new Error('Days of week must be integers between 0 (Sunday) and 6 (Saturday)');
          }
        }
        return true;
      })
  ];
}
