import { Request, Response } from 'express';
import { AuthService } from '../services/auth.service';
import { HttpException } from '../../../exceptions/HttpExceptions';
import { HttpStatus } from '../../../exceptions/HttpStatus';
import { ValidationHelpers } from '../../../utils/validation-helpers';

const authService = new AuthService();

/**
 * Get Google OAuth URL
 */
export const getGoogleAuthUrl = async (req: Request, res: Response): Promise<Response> => {
  try {
    const authUrl = authService.generateGoogleAuthUrl();
    
    return res.status(HttpStatus.OK).json({
      success: true,
      data: {
        authUrl,
      },
      message: 'Google OAuth URL generated successfully',
    });
  } catch (error) {
    console.error('Error generating Google auth URL:', error);
    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to generate authentication URL',
    });
  }
};

/**
 * Handle Google OAuth callback
 */
export const googleCallback = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { code } = req.body;

    if (!code) {
      throw HttpException.BadRequest('Authorization code is required');
    }

    const authResponse = await authService.exchangeCodeForTokens(code);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: authResponse,
      message: 'Authentication successful',
    });
  } catch (error) {
    console.error('Google callback error:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Authentication failed',
    });
  }
};

/**
 * Authenticate with Google ID token
 */
export const authenticateWithGoogle = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { idToken } = req.body;

    ValidationHelpers.validateRequiredFields(req.body, ['idToken']);

    const authResponse = await authService.authenticateWithGoogle(idToken);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: authResponse,
      message: 'Authentication successful',
    });
  } catch (error) {
    console.error('Google authentication error:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Authentication failed',
    });
  }
};

/**
 * Verify current token
 */
export const verifyToken = async (req: Request, res: Response): Promise<Response> => {
  try {
    const user = req.userOfficeManagement;

    if (!user) {
      throw HttpException.Unauthorized('No user found in request');
    }

    return res.status(HttpStatus.OK).json({
      success: true,
      data: {
        user: {
          userId: user.userId,
          email: user.email,
          role: user.role,
        },
      },
      message: 'Token is valid',
    });
  } catch (error) {
    console.error('Token verification error:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(HttpStatus.UNAUTHORIZED).json({
      success: false,
      message: 'Token verification failed',
    });
  }
};

/**
 * Logout user
 */
export const logout = async (req: Request, res: Response): Promise<Response> => {
  try {
    // In a real implementation, you might want to:
    // 1. Add the token to a blacklist
    // 2. Clear any session data
    // 3. Revoke the Google token if needed

    return res.status(HttpStatus.OK).json({
      success: true,
      message: 'Logout successful',
    });
  } catch (error) {
    console.error('Logout error:', error);
    
    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Logout failed',
    });
  }
};

/**
 * Get current user profile
 */
export const getProfile = async (req: Request, res: Response): Promise<Response> => {
  try {
    const user = req.userOfficeManagement;

    if (!user) {
      throw HttpException.Unauthorized('No user found in request');
    }

    // In a real implementation, you would fetch additional user data from the database
    return res.status(HttpStatus.OK).json({
      success: true,
      data: {
        user: {
          userId: user.userId,
          email: user.email,
          role: user.role,
        },
      },
      message: 'Profile retrieved successfully',
    });
  } catch (error) {
    console.error('Get profile error:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message,
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to retrieve profile',
    });
  }
};
