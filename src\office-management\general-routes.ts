import { Router } from 'express';
import { verifyTokenOfficeManagement } from './middlewares/verification-token';

const generalRoutes = Router();

// Test endpoint
generalRoutes.get('/test', verifyTokenOfficeManagement, (req, res) => {
  res.status(200).json({
    message: 'Office management platform test endpoint working',
    timestamp: new Date().toISOString(),
    path: req.path,
    method: req.method,
    user: req.userOfficeManagement
      ? {
          userId: req.userOfficeManagement.userId,
          email: req.userOfficeManagement.email,
          role: req.userOfficeManagement.role,
        }
      : null,
  });
});

// Health check endpoint
generalRoutes.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    service: 'office-management',
    timestamp: new Date().toISOString(),
  });
});

export default generalRoutes;
