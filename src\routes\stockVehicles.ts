import { Router } from 'express';
import {
  addStockVehicle,
  createAndUpdateCarPlates,
  createAndUpdateCirculationCard,
  createAndUpdateGPS,
  createAndUpdatePolicy,
  createAndUpdateTenancy,
  deleteStockVehicle,
  getAllStock,
  getStockVehicleById,
  queryGetStock,
  updateStockVehicle,
  filterStock,
  checkIfVehicleExist,
  editDescription,
  createContract,
  validateIfStockVehicleExistsByProps,
  createTijCirculationCard,
  getOlderCirculationCard,
  gpsInstalled,
  changeVehicleState,
  dashboardStatusData,
  dashboardStats,
  addRandomCarData,
  checkIftheLastAllPaymentDateIsTreeDaysBefore,
  deleteVehicleDocs,
  createAndUpdateSoldDocument,
  bulkUploadXML,
  initiateQrScanAction,
  confirmQrStatusChange,
  uploadQrScanPhoto,
  getVehiclesWithQRCodes,
  adminUpdatePhysicalStatus,
} from '../controllers/stockVehicles';
import { upload } from '../multer/multer';
import { multerArray } from '../constants';
import { checkSchema } from 'express-validator';
import postStockSchema from '../express-validator/associates/stockAddValidation';
import {
  stockEditValidator,
  stockPatchValidate,
  stockPatchValidateSingleDoc,
  stockPostValidator,
  validateAdminUpdatePhysicalStatus,
} from '../middlewares/stockValidators';
import updateDescSchema from '../express-validator/associates/stockDescUpValidation';
import stockPatchSchema from '../express-validator/associates/stockPatchValidation';
import { createReadmission, returnToStock } from '../controllers/Readmissions/readmissionVehicles';
import { dischargedVehicleById } from '../controllers/Discharged/dischargedPatch';
import {
  changeStockStatus,
  finishOverHaulingByVehicleId,
  returnToActiveAfterChangeStatus,
  sendOverHaulingByVehicleId,
} from '../controllers/ChangeStatus/patchStockStatus';
// import { sendInServiceVehicleId } from '../controllers/SendService/patchSendService';
import { getInsuranceByVehicleId, getServicesByVehicleId } from '../controllers/SendService/getServices';
import { getLegalProcessesById } from '../controllers/LegalProcess/getLegalProcess';
import { returnToVehicleReadyById } from '../modules/Stock/controllers/returnToVehicleReady';
import { canFinishProcessByVehicleId } from '../controllers/ChangeStatus/getStatusAndProcess';
import { getVehiclesByRol } from '../middlewares/stock/getVehiclesByRol';
import returnFlowRoute from '../modules/Flows/routes/return-flow.route';
import { getLastAssociatesData } from '../controllers/vehicles';
import { verifyToken } from '../middlewares/verifyToken';
import {
  generatePresignedUrlsForVehicleDocuments,
  processUploadedVehicleDocuments,
} from '../controllers/vehicleDocumentController';

const router = Router();
const circulationCard = [
  { name: 'frontImg', maxCount: 1 },
  { name: 'backImg', maxCount: 1 },
];
const carPlates = [
  { name: 'frontImg', maxCount: 1 },
  { name: 'backImg', maxCount: 1 },
  { name: 'platesDocument', maxCount: 1 },
];

const readmissionFiles = [
  { name: 'promissoryNoteTermination', maxCount: 1 },
  { name: 'agreementTermination', maxCount: 1 },
];

router.use('/flows', returnFlowRoute);

router.get('/get', getVehiclesByRol, getAllStock);
router.get('/query', queryGetStock);

router.get('/dashboardStatus/', dashboardStatusData);

router.get('/dashboardData/', dashboardStats);

router.get('/last-driver-data', getLastAssociatesData);

router.get('/getServices/:vehicleId', getServicesByVehicleId);
router.get('/getLegalProcesses/:vehicleId', getLegalProcessesById);
router.get('/getInsuranceServices/:vehicleId', getInsuranceByVehicleId);

router.put('/returnVehicleReady/:vehicleId', returnToVehicleReadyById);

router.get('/getById/:id', getStockVehicleById);

// Get vehicles with QR codes
router.get('/with-qr-codes', getVehiclesWithQRCodes);

// Routes for QR Scan Physical Status Update Flow
router.post('/:vehicleId/initiate-qr-scan-action', initiateQrScanAction);
router.post('/:vehicleId/confirm-qr-status-change', confirmQrStatusChange);
router.post('/upload-qr-photo/:vehicleId', upload.single('file'), uploadQrScanPhoto);

// Admin route to update physical status
router.post(
  '/:vehicleId/admin-update-physical-status',
  validateAdminUpdatePhysicalStatus,
  adminUpdatePhysicalStatus
);

router.post(
  '/add',
  upload.fields(multerArray),
  checkSchema(postStockSchema),
  stockPostValidator,
  addStockVehicle
);

router.post('/verifyVehicle', checkIfVehicleExist);

router.patch('/docs/add/:id');

router.patch(
  '/update/description/:vehicleId',
  upload.single('bill'),
  checkSchema(updateDescSchema),
  stockEditValidator,
  editDescription
);

/* PATCH */

router.patch(
  '/update/tenancy/:vehicleId',
  upload.single('tenancyDocument'),
  checkSchema(stockPatchSchema),
  stockPatchValidateSingleDoc,
  createAndUpdateTenancy
);

router.patch(
  '/update/policy/:vehicleId',
  upload.single('policyDocument'),
  checkSchema(stockPatchSchema),
  stockPatchValidateSingleDoc,
  createAndUpdatePolicy
);

router.patch('/update/gps/:vehicleId', createAndUpdateGPS);

router.patch(
  '/docs/contract/:vehicleId',
  upload.single('contract'),
  stockPatchValidateSingleDoc,
  createContract
);

router.patch(
  '/update/circulationCard/:vehicleId',
  upload.fields(circulationCard),
  checkSchema(stockPatchSchema),
  stockPatchValidate,
  createAndUpdateCirculationCard
);

router.patch(
  '/update/circulationCard/tij/:vehicleId',
  upload.fields(circulationCard),
  checkSchema(stockPatchSchema),
  stockPatchValidate,
  createTijCirculationCard
);

router.patch(
  '/update/salesDocuments/:vehicleId',
  upload.single('soldDocument'),
  checkSchema(stockPatchSchema),
  stockPatchValidateSingleDoc,
  createAndUpdateSoldDocument
);

/* VEHICLE DOCUMENT BULK UPLOAD ROUTES */
router.post('/documents/generate-upload-urls', verifyToken, generatePresignedUrlsForVehicleDocuments);

router.post('/documents/process-bulk-upload', verifyToken, processUploadedVehicleDocuments);

router.get('/circulationCard/tij/:vehicleId', getOlderCirculationCard);

router.patch(
  '/update/carPlates/:vehicleId',
  upload.fields(carPlates),
  checkSchema(stockPatchSchema),
  stockPatchValidate,
  createAndUpdateCarPlates
);

router.patch('/sendReadmission/:id', upload.fields(readmissionFiles), createReadmission);

router.patch('/update/:id', updateStockVehicle);

const imagesAndFiles = [
  { name: 'kmImgs', maxCount: Infinity },
  { name: 'evidenceImgs', maxCount: Infinity },
  { name: 'promissoryNote', maxCount: 1 },
  { name: 'readmissionDoc', maxCount: 1 },
  { name: 'contractCanceled', maxCount: 1 },
  { name: 'signedPromissoryNote', maxCount: 1 },
  { name: 'agreementSigned', maxCount: 1 },
  { name: 'recessionSigned', maxCount: 1 },
];

const dischargedFiles = [
  { name: 'platesDischargedDoc', maxCount: 1 },
  { name: 'reportDoc', maxCount: 1 },
  { name: 'dictamenDoc', maxCount: 1 },
];

/* GPS INSTALLED */

router.patch('/gps-installation/:vehicleId', gpsInstalled);

/* CHANGE STATUS */

router.patch('/returnToStock/:id', upload.fields(imagesAndFiles), returnToStock);
router.patch('/sendDischarged/:id', upload.fields(dischargedFiles), dischargedVehicleById);

/* OVERHAULING STATUS (ESTATUS EN REVISIÓN) */
router.patch('/sendOverHauling/:vehicleId', upload.any(), sendOverHaulingByVehicleId);
router.patch('/finishOverHauling/:vehicleId', upload.any(), finishOverHaulingByVehicleId);

/* CHANGE STATUS PROCESS */
router.patch('/changeStatus-processes/:vehicleId', changeStockStatus);
router.patch('/changeStatus-finish-process/:vehicleId', upload.any(), returnToActiveAfterChangeStatus);

router.get('/changeStatus-validate-finish-process/:id', canFinishProcessByVehicleId);

router.post('/validate-fields', validateIfStockVehicleExistsByProps);

router.delete('/delete/:id', deleteStockVehicle);
router.get('/search/', getVehiclesByRol, filterStock);
router.post('/change-state/', changeVehicleState);

router.post('/add-random-data', addRandomCarData);
router.get('/check-last-payment-date/', checkIftheLastAllPaymentDateIsTreeDaysBefore);

router.patch('/removeVehicleDocs', deleteVehicleDocs);

router.post('/bulk-upload', upload.array('files'), bulkUploadXML);

export default router;
