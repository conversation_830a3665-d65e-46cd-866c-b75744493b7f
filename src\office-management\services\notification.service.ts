import { NotificationType, SLACK_CONFIG, WHATSAPP_CONFIG } from '../constants';

export interface NotificationData {
  type: NotificationType;
  recipient: string;
  subject?: string;
  message: string;
  data?: any;
}

export class NotificationService {
  /**
   * Send notification based on type
   */
  async sendNotification(notificationData: NotificationData): Promise<boolean> {
    try {
      switch (notificationData.type) {
        case NotificationType.SLACK:
          return await this.sendSlackNotification(notificationData);
        case NotificationType.EMAIL:
          return await this.sendEmailNotification(notificationData);
        case NotificationType.WHATSAPP:
          return await this.sendWhatsAppNotification(notificationData);
        default:
          console.error('Unknown notification type:', notificationData.type);
          return false;
      }
    } catch (error) {
      console.error('Error sending notification:', error);
      return false;
    }
  }

  /**
   * Send Slack notification
   */
  private async sendSlackNotification(data: NotificationData): Promise<boolean> {
    if (!SLACK_CONFIG.WEBHOOK_URL) {
      console.warn('Slack webhook URL not configured');
      return false;
    }

    try {
      const response = await fetch(SLACK_CONFIG.WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          channel: data.recipient || SLACK_CONFIG.CHANNEL,
          username: SLACK_CONFIG.BOT_NAME,
          text: data.message,
          attachments: data.data?.attachments || [],
        }),
      });

      return response.ok;
    } catch (error) {
      console.error('Error sending Slack notification:', error);
      return false;
    }
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(data: NotificationData): Promise<boolean> {
    // This would integrate with your email service (SendGrid, AWS SES, etc.)
    console.log('Sending email notification:', {
      to: data.recipient,
      subject: data.subject,
      message: data.message,
    });

    // Placeholder for actual email implementation
    return true;
  }

  /**
   * Send WhatsApp notification
   */
  private async sendWhatsAppNotification(data: NotificationData): Promise<boolean> {
    if (!WHATSAPP_CONFIG.API_URL || !WHATSAPP_CONFIG.API_KEY) {
      console.warn('WhatsApp API not configured');
      return false;
    }

    try {
      const response = await fetch(WHATSAPP_CONFIG.API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${WHATSAPP_CONFIG.API_KEY}`,
        },
        body: JSON.stringify({
          to: data.recipient,
          message: data.message,
        }),
      });

      return response.ok;
    } catch (error) {
      console.error('Error sending WhatsApp notification:', error);
      return false;
    }
  }

  /**
   * Send booking confirmation
   */
  async sendBookingConfirmation(
    userEmail: string,
    bookingDetails: any,
    slackChannel?: string
  ): Promise<void> {
    const message = this.formatBookingMessage(bookingDetails);

    // Send Slack notification
    await this.sendNotification({
      type: NotificationType.SLACK,
      recipient: slackChannel || SLACK_CONFIG.CHANNEL,
      message,
      data: {
        attachments: [
          {
            color: 'good',
            title: 'New Booking Created',
            fields: [
              {
                title: 'Type',
                value: bookingDetails.type,
                short: true,
              },
              {
                title: 'Date',
                value: bookingDetails.date,
                short: true,
              },
              {
                title: 'User',
                value: userEmail,
                short: true,
              },
            ],
          },
        ],
      },
    });

    // Send email confirmation
    await this.sendNotification({
      type: NotificationType.EMAIL,
      recipient: userEmail,
      subject: 'Booking Confirmation',
      message,
    });
  }

  /**
   * Send guest invitation
   */
  async sendGuestInvitation(
    guestContact: string,
    contactType: 'email' | 'phone',
    invitationDetails: any
  ): Promise<void> {
    const message = this.formatGuestInvitationMessage(invitationDetails);

    const notificationType = contactType === 'email' ? NotificationType.EMAIL : NotificationType.WHATSAPP;

    await this.sendNotification({
      type: notificationType,
      recipient: guestContact,
      subject: contactType === 'email' ? 'Office Visit Invitation' : undefined,
      message,
    });
  }

  /**
   * Format booking message
   */
  private formatBookingMessage(bookingDetails: any): string {
    return `
🏢 New Office Booking

Type: ${bookingDetails.type}
Date: ${bookingDetails.date}
Time: ${bookingDetails.time || 'Full day'}
Location: ${bookingDetails.location}
User: ${bookingDetails.userEmail}

${bookingDetails.type === 'meeting_room' ? `Duration: ${bookingDetails.duration} hours` : ''}
    `.trim();
  }

  /**
   * Format guest invitation message
   */
  private formatGuestInvitationMessage(invitationDetails: any): string {
    return `
🏢 OneCarNow Office Visit Invitation

You have been invited to visit our office:

Date: ${invitationDetails.date}
Time: ${invitationDetails.time}
Host: ${invitationDetails.hostName}
Location: ${invitationDetails.location}

Please arrive at the reception desk and mention your host's name.

For any questions, please contact your host.
    `.trim();
  }
}
