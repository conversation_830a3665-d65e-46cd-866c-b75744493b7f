import { Request, Response } from 'express';
import { BookingsService } from '../services/bookings.service';
import { HttpStatus } from '../../../exceptions/HttpStatus';
import { HttpException } from '../../../exceptions/HttpExceptions';
import { ValidationHelpers } from '../../../utils/validation-helpers';

const bookingsService = new BookingsService();

/**
 * Create a new booking
 */
export const createBooking = async (req: Request, res: Response): Promise<Response> => {
  try {
    const userId = req.userOfficeManagement?.userId;
    
    if (!userId) {
      throw HttpException.Unauthorized('User not authenticated');
    }

    const requiredFields = ['resourceType', 'resourceId', 'title', 'startDate', 'endDate'];
    ValidationHelpers.validateRequiredFields(req.body, requiredFields);

    const { resourceType, resourceId, title, description, startDate, endDate, attendees } = req.body;

    if (!ValidationHelpers.isValidObjectId(resourceId)) {
      throw HttpException.BadRequest('Invalid resource ID');
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      throw HttpException.BadRequest('Invalid date format');
    }

    if (start >= end) {
      throw HttpException.BadRequest('Start date must be before end date');
    }

    const booking = await bookingsService.createBooking({
      userId,
      resourceType,
      resourceId,
      title,
      description,
      startDate: start,
      endDate: end,
      attendees
    });

    return res.status(HttpStatus.CREATED).json({
      success: true,
      data: booking,
      message: 'Booking created successfully'
    });
  } catch (error) {
    console.error('Error creating booking:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to create booking'
    });
  }
};

/**
 * Get user's bookings
 */
export const getUserBookings = async (req: Request, res: Response): Promise<Response> => {
  try {
    const userId = req.userOfficeManagement?.userId;
    
    if (!userId) {
      throw HttpException.Unauthorized('User not authenticated');
    }

    const { startDate, endDate, status, resourceType, limit, skip } = req.query;

    const options: any = {};

    if (startDate) {
      options.startDate = new Date(startDate as string);
      if (isNaN(options.startDate.getTime())) {
        throw HttpException.BadRequest('Invalid start date format');
      }
    }

    if (endDate) {
      options.endDate = new Date(endDate as string);
      if (isNaN(options.endDate.getTime())) {
        throw HttpException.BadRequest('Invalid end date format');
      }
    }

    if (status) {
      options.status = Array.isArray(status) ? status : [status];
    }

    if (resourceType) {
      options.resourceType = resourceType;
    }

    if (limit) {
      options.limit = parseInt(limit as string);
    }

    if (skip) {
      options.skip = parseInt(skip as string);
    }

    const bookings = await bookingsService.getUserBookings(userId, options);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: bookings,
      message: 'Bookings retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting user bookings:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to retrieve bookings'
    });
  }
};

/**
 * Get booking by ID
 */
export const getBookingById = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const userId = req.userOfficeManagement?.userId;
    
    if (!ValidationHelpers.isValidObjectId(id)) {
      throw HttpException.BadRequest('Invalid booking ID');
    }

    const booking = await bookingsService.getBookingById(id, userId);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: booking,
      message: 'Booking retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting booking:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to retrieve booking'
    });
  }
};

/**
 * Update booking
 */
export const updateBooking = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const userId = req.userOfficeManagement?.userId;
    
    if (!userId) {
      throw HttpException.Unauthorized('User not authenticated');
    }

    if (!ValidationHelpers.isValidObjectId(id)) {
      throw HttpException.BadRequest('Invalid booking ID');
    }

    // Validate dates if provided
    if (req.body.startDate) {
      const startDate = new Date(req.body.startDate);
      if (isNaN(startDate.getTime())) {
        throw HttpException.BadRequest('Invalid start date format');
      }
      req.body.startDate = startDate;
    }

    if (req.body.endDate) {
      const endDate = new Date(req.body.endDate);
      if (isNaN(endDate.getTime())) {
        throw HttpException.BadRequest('Invalid end date format');
      }
      req.body.endDate = endDate;
    }

    const booking = await bookingsService.updateBooking(id, userId, req.body);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: booking,
      message: 'Booking updated successfully'
    });
  } catch (error) {
    console.error('Error updating booking:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to update booking'
    });
  }
};

/**
 * Cancel booking
 */
export const cancelBooking = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const userId = req.userOfficeManagement?.userId;
    
    if (!userId) {
      throw HttpException.Unauthorized('User not authenticated');
    }

    if (!ValidationHelpers.isValidObjectId(id)) {
      throw HttpException.BadRequest('Invalid booking ID');
    }

    const booking = await bookingsService.cancelBooking(id, userId, reason);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: booking,
      message: 'Booking cancelled successfully'
    });
  } catch (error) {
    console.error('Error cancelling booking:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to cancel booking'
    });
  }
};

/**
 * Get all bookings (Admin only)
 */
export const getAllBookings = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { startDate, endDate, status, resourceType, resourceId, limit, skip } = req.query;

    const options: any = {};

    if (startDate) {
      options.startDate = new Date(startDate as string);
      if (isNaN(options.startDate.getTime())) {
        throw HttpException.BadRequest('Invalid start date format');
      }
    }

    if (endDate) {
      options.endDate = new Date(endDate as string);
      if (isNaN(options.endDate.getTime())) {
        throw HttpException.BadRequest('Invalid end date format');
      }
    }

    if (status) {
      options.status = Array.isArray(status) ? status : [status];
    }

    if (resourceType) {
      options.resourceType = resourceType;
    }

    if (resourceId) {
      if (!ValidationHelpers.isValidObjectId(resourceId as string)) {
        throw HttpException.BadRequest('Invalid resource ID');
      }
      options.resourceId = resourceId;
    }

    if (limit) {
      options.limit = parseInt(limit as string);
    }

    if (skip) {
      options.skip = parseInt(skip as string);
    }

    const bookings = await bookingsService.getAllBookings(options);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: bookings,
      message: 'All bookings retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting all bookings:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to retrieve bookings'
    });
  }
};

/**
 * Get booking statistics
 */
export const getBookingStats = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { startDate, endDate } = req.query;
    const userId = req.userOfficeManagement?.userId;
    const userRole = req.userOfficeManagement?.role;

    const options: any = {};

    if (startDate) {
      options.startDate = new Date(startDate as string);
      if (isNaN(options.startDate.getTime())) {
        throw HttpException.BadRequest('Invalid start date format');
      }
    }

    if (endDate) {
      options.endDate = new Date(endDate as string);
      if (isNaN(options.endDate.getTime())) {
        throw HttpException.BadRequest('Invalid end date format');
      }
    }

    // If not admin, only show user's own stats
    if (userRole !== 'admin') {
      options.userId = userId;
    }

    const stats = await bookingsService.getBookingStats(options);

    return res.status(HttpStatus.OK).json({
      success: true,
      data: stats,
      message: 'Booking statistics retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting booking stats:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to retrieve booking statistics'
    });
  }
};

/**
 * Get today's bookings
 */
export const getTodayBookings = async (req: Request, res: Response): Promise<Response> => {
  try {
    const bookings = await bookingsService.getTodayBookings();

    return res.status(HttpStatus.OK).json({
      success: true,
      data: bookings,
      message: "Today's bookings retrieved successfully"
    });
  } catch (error) {
    console.error('Error getting today bookings:', error);
    
    if (error instanceof HttpException) {
      return res.status(error.status).json({
        success: false,
        message: error.message
      });
    }

    return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to retrieve today\'s bookings'
    });
  }
};
