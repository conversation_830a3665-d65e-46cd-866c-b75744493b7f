/* eslint-disable @typescript-eslint/no-use-before-define */
import { PAYMENTS_API_KEY, PAYMENTS_API_URL } from '../../../constants/payments-api';
import { MyRequest } from '../../../types&interfaces/interfaces';
import { getCurrentDateFromTimezone /* getCurrentMockDate */ } from '../../../services/timestamps';
import { isBeforeThursday5PM } from '../lib/dates';
import { isSameWeek, nextDay, nextMonday, parse, previousThursday } from 'date-fns';
import { DateTime } from 'luxon';
import TempSuscriptionPayments from '../../TempSuscriptionPayments/model/tempSuscriptionPayment.model';
import { AssociatePaymentInstanceType } from '../../../models/associatePayments';
import { MainContractInstanceType } from '../../../models/mainContractSchema';
import { AssociateInstanceType } from '../../../models/associateSchema';
import { CustomError } from '../../../services/customErrors';
import axios from 'axios';

type DivideWeeksParams = {
  req: MyRequest;
  associatePayment: AssociatePaymentInstanceType;
  mainContract: MainContractInstanceType;
  associate: AssociateInstanceType;
};
export async function divideWeeks({ req, associatePayment, mainContract, associate }: DivideWeeksParams) {
  const weeksChanged =
    (req.body.weeksChanged as {
      index: number;
      weeklyRent: number;
      number: string;
      day: string;
      fee?: number;
    }[]) || null;
  if (weeksChanged) {
    // const first = weeksChanged[0].index;
    // console.log('weeksChanged', weeksChanged);
    associatePayment.paymentsArray.forEach((el, index) => {
      const found = weeksChanged.find((w) => w.index === index);
      if (found) {
        // If the week is found in the weeksChanged array, we will update the weeklyCost with the new value
        associatePayment.paymentsArray[index] = {
          ...el,
          weeklyCost: found.weeklyRent,
          fee: found.fee || 0,
        };
      } else {
        // If not, we will update the weeklyCost with the main contract weekly rent
        associatePayment.paymentsArray[index] = {
          ...el,
          weeklyCost: mainContract?.weeklyRent || el.weeklyCost,
          fee: 0,
        };
      }
    });

    if (mainContract) {
      // const endDateChange = mainContract.allPayments[first + length - 1].day;
      const endDateChange = weeksChanged.slice(-1)[0].day;
      // console.log('LENGTH', length);
      console.log('endDateChange', endDateChange);
      const endDateParse = parse(endDateChange, 'dd-MM-yyyy', new Date());
      console.log('endDateParse', endDateParse);
      // const amount = weeksChanged.slice(-1)[0].weeklyRent - mainContract.weeklyRent; // get the difference between the new weekly rent and the old one
      // console.log('amount', amount);

      const weeklyRent = mainContract.weeklyRent;
      let amount = weeksChanged.slice(-1)[0].weeklyRent - weeklyRent;

      // const lastWeek = weeksChanged.slice(-1)[0];
      const fee = weeksChanged.slice(-1)[0].fee || 0;
      if (req.body.addAmount > 0 && req.body.divideWeeks && fee > 0) {
        const feeToSum = fee || +(req.body.addAmount / req.body.divideWeeks).toFixed(2);
        amount = amount + feeToSum;
      }

      const adendumDataProduct = {
        tempItems: [
          {
            name: 'Adendum de contrato',
            description: 'División de semanas',
            quantity: 1,
            temp: true,
            total: amount,
            expirationDate: endDateParse, // this field is to save in our database, not to send it to gigstack
            taxes: [
              {
                rate: 0.16,
                factor: 'Tasa',
                withholding: false,
                type: 'IVA',
                inclusive: true,
              },
            ],
          },
        ],
      };

      let currentDate = getCurrentDateFromTimezone();
      // let currentDate = getCurrentMockDate('2024-10-04');

      console.log('currentDate', currentDate);
      const copyCurrentDate = getCurrentDateFromTimezone();
      console.log('copyCurrentDate', copyCurrentDate);

      let activationDate: Date | null = null;
      // let activationDateNoChanges: Date | null = null;
      // let dateStopSubscriptionWith0: Date | null = null;

      const findActivationDate = weeksChanged.findIndex(
        (w) => w.weeklyRent !== 0 && w.weeklyRent !== mainContract?.weeklyRent
      );
      const firstIndex0Amount = weeksChanged.findIndex((w) => w.weeklyRent === 0);

      if (findActivationDate === -1 || firstIndex0Amount === -1) {
        // if there is no 0 amount, we will change activate the temp products right now

        await activateWhenNo0Weeks({
          associatePayment,
          associate,
          adendumDataProduct,
        });

        return;
        // throw new CustomError('No se encontró ningun pago 0 y ningun pago con cambios', 400);
      }

      const activationDateFound = weeksChanged[findActivationDate].day;
      activationDate = previousThursday(parse(activationDateFound, 'dd-MM-yyyy', new Date()));

      if (findActivationDate < firstIndex0Amount) {
        throw new CustomError('La fecha de activación no puede ser menor a la fecha de suspensión', 400);
      }

      const existNormalPaymentBetween = weeksChanged.find((w) => w.weeklyRent === mainContract.weeklyRent);

      // console.log('existNormalPaymentBetween', existNormalPaymentBetween);
      let filterNormalPaymentsBetween: typeof weeksChanged = [];
      if (existNormalPaymentBetween) {
        filterNormalPaymentsBetween = weeksChanged.filter((w) => w.weeklyRent === mainContract.weeklyRent);
      }

      let dateToAddTempProducts: Date | null = null;
      if (filterNormalPaymentsBetween.length > 0) {
        const firstNormalPaymentBetween = filterNormalPaymentsBetween[0];

        // activationDate = parse(firstNormalPaymentBetween.day, 'dd-MM-yyyy', new Date());

        activationDate = previousThursday(parse(firstNormalPaymentBetween.day, 'dd-MM-yyyy', new Date()));

        const lastNormalPaymentBetween = filterNormalPaymentsBetween.slice(-1)[0];

        const day = lastNormalPaymentBetween.day;

        const dateParsed = parse(day, 'dd-MM-yyyy', new Date());

        const nextThursdayFromLastNormalPayment = nextDay(dateParsed, 4);

        dateToAddTempProducts = nextThursdayFromLastNormalPayment;
      }

      // ✅ validate if current date is after thursday 5pm
      // ✅ if is after thursday 5pm, we should stop the subscription
      // ✅ and activate it later with the new payment amount saved in tempSuscriptionPayments
      // ✅ in another handler ran by a cron job

      // const specficDatetime = DateTime.fromJSDate(currentDate).setZone('utc');
      // console.log('SPECIFIC DATETIME', specficDatetime);
      const isBefore = isBeforeThursday5PM();
      // const isBefore = isBeforeThursday5PM();
      console.log('IS BEFORE', isBefore);

      const stopDate = weeksChanged[firstIndex0Amount].day;

      const stopDateParsed = parse(stopDate, 'dd-MM-yyyy', new Date());
      const dateweek = DateTime.fromJSDate(stopDateParsed).minus({ days: 2 }).toJSDate();

      const previousWeekpay = previousThursday(stopDateParsed);
      console.log('stopDateParsed', stopDateParsed);
      console.log('previousWeekpay', previousWeekpay);
      const isSameWeek2 = isSameWeek(currentDate, dateweek, { weekStartsOn: 0 });
      console.log('IS SAME WEEK', isSameWeek2);
      console.log('comparision', currentDate, dateweek);

      const tempSuscriptionFound = await TempSuscriptionPayments.findOne({
        associatePaymentId: associatePayment._id,
      }).sort({ createdAt: -1 });

      if (tempSuscriptionFound) {
        // if tempSuscriptionFound exists, we will update the tempItems with the new data
        // and set status to pending
        tempSuscriptionFound.tempItems = adendumDataProduct.tempItems;
        if (activationDate) {
          tempSuscriptionFound.activationDate = activationDate;
          tempSuscriptionFound.dateToAddTempProducts = dateToAddTempProducts!;
          tempSuscriptionFound.status = 'pending';
        }
        if (!isSameWeek2) tempSuscriptionFound.stopDate = stopDateParsed;
        if (isSameWeek2) tempSuscriptionFound.stopDate = null!;

        await tempSuscriptionFound.save();
      } else {
        const tempSuscription = new TempSuscriptionPayments({
          associateId: associatePayment.associateId,
          associatePaymentId: associatePayment._id,
          stockId: associatePayment.vehiclesId,
          tempItems: adendumDataProduct.tempItems,
          status: 'pending',
          region: associatePayment.region || 'TEST',
          suscriptionId: associate?.clientId,
          activationDate: activationDate ? nextMonday(activationDate) : null,
          dateToAddTempProducts: dateToAddTempProducts ? nextMonday(dateToAddTempProducts) : null,
        });

        await tempSuscription.save();
      }

      if (isBefore) {
        // STARTING code if is before thursday 5pm, we should add the products now ------------------------------------
        console.log('ES ANTES DE LAS 5PM');

        if (associate.clientId) {
          // stop subscription
          await axios.patch(
            `${PAYMENTS_API_URL}/subscriptions/status/${associate.clientId}`,
            {
              status: false,
            },
            {
              headers: {
                Authorization: `Bearer ${PAYMENTS_API_KEY}`,
              },
            }
          );
        }

        // FINISHING code if is before thursday 5pm, we should add the products now ------------------------------------
      }
      if (!isBefore) {
        console.log('NO ES ANTES DE LAS 5PM');

        const first0Amount = weeksChanged.findIndex((w) => w.weeklyRent === 0);
        const last0Amount = weeksChanged.findLastIndexCustom((w) => w.weeklyRent === 0);

        if (first0Amount !== -1 && last0Amount !== -1) {
          const firstLastThursday = previousThursday(
            parse(weeksChanged[first0Amount].day, 'dd-MM-yyyy', new Date())
          );
          const lastLastThursday = previousThursday(
            parse(weeksChanged[last0Amount].day, 'dd-MM-yyyy', new Date())
          );

          if (currentDate >= firstLastThursday && currentDate <= lastLastThursday) {
            // If is not before thursday 5pm but is between the dates of suspension, we should stop the subscription
            console.log('SE GENERÓ ENTRE LAS FECHAS DE SUSPENSIÓN --------------------');
            await axios.patch(
              `${PAYMENTS_API_URL}/subscriptions/status/${associate.clientId}`,
              {
                status: false,
              },
              {
                headers: {
                  Authorization: `Bearer ${PAYMENTS_API_KEY}`,
                },
              }
            );
          }
        }
        // delete last payments in api
        // and stop subscription
        const url = new URL(`${PAYMENTS_API_URL}/payments`);

        const start = parse(weeksChanged[first0Amount].day, 'dd-MM-yyyy', new Date());

        // const lastThursday = nextDay(start, 4);
        const lastThursday = previousThursday(start);

        const startDate = lastThursday.toISOString().split('T')[0] + 'T00:00:00.000Z';

        const endDate =
          parse(weeksChanged.slice(-1)[0].day, 'dd-MM-yyyy', new Date()).toISOString().split('T')[0] +
          'T23:59:59.000Z';

        const params = {
          clientId: associate.clientId,
          startDate,
          endDate,
          status: 'pending',
          isPaid: 'false',
          total: mainContract.weeklyRent + '',
        };

        url.search = new URLSearchParams(params).toString();

        const { data } = await axios.get(url.toString(), {
          headers: {
            Authorization: `Bearer ${PAYMENTS_API_KEY}`,
          },
        });

        const payments = data.data;

        await Promise.allSettled(
          payments.map(async (p: any) => {
            try {
              await axios.patch(
                `${PAYMENTS_API_URL}/payments/${p.id}/cancel`,
                {
                  reason: 'Adendum de contrato',
                },
                {
                  headers: {
                    Authorization: `Bearer ${PAYMENTS_API_KEY}`,
                  },
                }
              );
            } catch (error: any) {
              console.log('ERROR', error?.response?.data);
            }
          })
        );
      }
    }
  }
}

async function activateWhenNo0Weeks({
  associatePayment,
  associate,
  adendumDataProduct,
}: {
  associatePayment: any;
  associate: any;
  adendumDataProduct: { tempItems: any[] };
}) {
  const tempSuscriptionFound = await TempSuscriptionPayments.findOne({
    associatePaymentId: associatePayment._id,
  }).sort({ createdAt: -1 });
  console.log('FOUND OR NOT FOUND', tempSuscriptionFound);
  if (tempSuscriptionFound) {
    // if tempSuscriptionFound exists, we will update the tempItems with the new data
    // and set status to pending
    tempSuscriptionFound.tempItems = adendumDataProduct.tempItems;
    tempSuscriptionFound.status = 'active';

    await tempSuscriptionFound.save();
  } else {
    const tempSuscription = new TempSuscriptionPayments({
      associateId: associatePayment.associateId,
      associatePaymentId: associatePayment._id,
      stockId: associatePayment.vehiclesId,
      tempItems: adendumDataProduct.tempItems,
      status: 'active',
      region: associatePayment.region || 'TEST',
      suscriptionId: associate?.clientId,
      activationDate: null,
      // dateToAddTempProducts,
    });
    await tempSuscription.save();
  }

  const formatTempItems = adendumDataProduct.tempItems.map((item) => {
    return {
      name: item.name,
      description: item.description,
      quantity: item.quantity,
      price: item.total,
      isTemporal: true,
    };
  });

  // remove temporal products for those cases that download the pdf more than once, we need to remove the previous
  // added temporal products to avoid adding the same products multiple times and increase the total price
  await axios.patch(
    `${PAYMENTS_API_URL}/subscriptions/${associate.clientId}/remove-temporal-products`,
    {},
    {
      headers: {
        Authorization: `Bearer ${PAYMENTS_API_KEY}`,
      },
    }
  );

  // activate subscription

  await axios.patch(
    `${PAYMENTS_API_URL}/subscriptions/status/${associate.clientId}`,
    {
      status: true,
    },
    {
      headers: {
        Authorization: `Bearer ${PAYMENTS_API_KEY}`,
      },
    }
  );

  // add temporal products
  await axios.patch(
    `${PAYMENTS_API_URL}/subscriptions/${associate.clientId}/add-temporal-products`,
    {
      temporalProducts: formatTempItems,
    },
    {
      headers: {
        Authorization: `Bearer ${PAYMENTS_API_KEY}`,
      },
    }
  );
}
