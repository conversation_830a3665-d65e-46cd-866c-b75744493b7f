import { Router } from 'express';
import authRouter from './modules/auth/routes/auth.routes';
import organizationsRouter from './modules/organizations/routes/organizations.routes';
import vehiclesRouter from './modules/stockVehicles/routes/vehicles.routes';
import servicesRouter from './modules/services/routes/service.routes';
import workshopRouter from './modules/workshop/routes/workshop.routes';
import ScheduleOverride from './modules/scheduleOverride/routes/scheduleOverride.routes';
import serviceTypesRouter from './modules/serviceType/routes/serviceType.routes';
import appointmentRouter from './modules/appointments/routes/appointments.routes';
import installationRouter from './modules/company/routes/installation.routes';
import correctiveMaintenanceRouter from './modules/corrective-maintenance/routes/corrective-maintenance.routes';
import companyRouter from './modules/company/routes/company.routes';
import cityRouter from './modules/cities/routes/city.routes';
import crewRouter from './modules/crews/routes/crew.routes';
import neighborhoodRouter from './modules/neighborhoods/routes/neighborhood.routes';
import usersRouter from './modules/users/routes/users.routes';
import generalRoutes from './general-routes';
import gestoresRouter from './modules/gestores/routes/gestores.routes';
import procedimientosRouter from './modules/gestores/routes/procedimientos.routes';
import tramitesRouter from './modules/gestores/routes/tramites.routes';

const vendorPlatformMainRouter = Router();

export const vendorUrl = '/vendor-platform';

vendorPlatformMainRouter.use(vendorUrl, generalRoutes);

vendorPlatformMainRouter.use(vendorUrl, authRouter);
vendorPlatformMainRouter.use(vendorUrl, usersRouter);

vendorPlatformMainRouter.use(vendorUrl, organizationsRouter);
vendorPlatformMainRouter.use(vendorUrl, vehiclesRouter);
vendorPlatformMainRouter.use(vendorUrl, servicesRouter);
vendorPlatformMainRouter.use(vendorUrl, workshopRouter);
vendorPlatformMainRouter.use(vendorUrl, ScheduleOverride);
vendorPlatformMainRouter.use(vendorUrl, serviceTypesRouter);
vendorPlatformMainRouter.use(vendorUrl, appointmentRouter);

// Companies routes

vendorPlatformMainRouter.use(vendorUrl, crewRouter);
vendorPlatformMainRouter.use(vendorUrl, companyRouter);
vendorPlatformMainRouter.use(vendorUrl, installationRouter);
vendorPlatformMainRouter.use(vendorUrl, cityRouter);
vendorPlatformMainRouter.use(vendorUrl, neighborhoodRouter);

// Gestores routes

vendorPlatformMainRouter.use(vendorUrl, gestoresRouter);
vendorPlatformMainRouter.use(vendorUrl, procedimientosRouter);
vendorPlatformMainRouter.use(vendorUrl, tramitesRouter);

// Corrective Maintenance routes
vendorPlatformMainRouter.use(vendorUrl, correctiveMaintenanceRouter);

export default vendorPlatformMainRouter;
