import nodemailer from 'nodemailer';
import { NotificationData, NotificationResult } from './notification.service';

export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
  }

  async sendBookingConfirmation(data: NotificationData): Promise<NotificationResult> {
    const { booking, user, meetingRoom } = data;
    
    if (!booking || !user || !meetingRoom) {
      throw new Error('Missing required data for booking confirmation');
    }

    const subject = `Booking Confirmed: ${meetingRoom.name}`;
    const html = this.generateBookingConfirmationHTML(booking, user, meetingRoom);

    try {
      const info = await this.transporter.sendMail({
        from: process.env.SMTP_FROM || '<EMAIL>',
        to: user.email,
        subject,
        html
      });

      return {
        success: true,
        messageId: info.messageId,
        sentAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: new Date()
      };
    }
  }

  async sendBookingReminder(data: NotificationData): Promise<NotificationResult> {
    const { booking, user, meetingRoom } = data;
    
    if (!booking || !user || !meetingRoom) {
      throw new Error('Missing required data for booking reminder');
    }

    const subject = `Reminder: Meeting in ${meetingRoom.name} starts soon`;
    const html = this.generateBookingReminderHTML(booking, user, meetingRoom);

    try {
      const info = await this.transporter.sendMail({
        from: process.env.SMTP_FROM || '<EMAIL>',
        to: user.email,
        subject,
        html
      });

      return {
        success: true,
        messageId: info.messageId,
        sentAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: new Date()
      };
    }
  }

  async sendBookingCancellation(data: NotificationData): Promise<NotificationResult> {
    const { booking, user, meetingRoom } = data;
    
    if (!booking || !user || !meetingRoom) {
      throw new Error('Missing required data for booking cancellation');
    }

    const subject = `Booking Cancelled: ${meetingRoom.name}`;
    const html = this.generateBookingCancellationHTML(booking, user, meetingRoom);

    try {
      const info = await this.transporter.sendMail({
        from: process.env.SMTP_FROM || '<EMAIL>',
        to: user.email,
        subject,
        html
      });

      return {
        success: true,
        messageId: info.messageId,
        sentAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: new Date()
      };
    }
  }

  async sendGuestInvitation(data: NotificationData): Promise<NotificationResult> {
    const { guest, user } = data;
    
    if (!guest || !user || !guest.email) {
      throw new Error('Missing required data for guest invitation');
    }

    const subject = `You're invited to visit ${user.firstName} ${user.lastName}`;
    const html = this.generateGuestInvitationHTML(guest, user);

    try {
      const info = await this.transporter.sendMail({
        from: process.env.SMTP_FROM || '<EMAIL>',
        to: guest.email,
        subject,
        html
      });

      return {
        success: true,
        messageId: info.messageId,
        sentAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: new Date()
      };
    }
  }

  async notifyHostOfGuestArrival(data: NotificationData): Promise<NotificationResult> {
    const { guest, user } = data;
    
    if (!guest || !user) {
      throw new Error('Missing required data for guest arrival notification');
    }

    const subject = `Your guest ${guest.name} has arrived`;
    const html = this.generateGuestArrivalHTML(guest, user);

    try {
      const info = await this.transporter.sendMail({
        from: process.env.SMTP_FROM || '<EMAIL>',
        to: user.email,
        subject,
        html
      });

      return {
        success: true,
        messageId: info.messageId,
        sentAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: new Date()
      };
    }
  }

  async sendCustomMessage(data: NotificationData): Promise<NotificationResult> {
    const { user, customSubject, customMessage } = data;
    
    if (!user || !customSubject || !customMessage) {
      throw new Error('Missing required data for custom message');
    }

    try {
      const info = await this.transporter.sendMail({
        from: process.env.SMTP_FROM || '<EMAIL>',
        to: user.email,
        subject: customSubject,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>Message from Office Management</h2>
            <p>Hello ${user.firstName},</p>
            <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
              ${customMessage}
            </div>
            <p>Best regards,<br>Office Management Team</p>
          </div>
        `
      });

      return {
        success: true,
        messageId: info.messageId,
        sentAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: new Date()
      };
    }
  }

  private generateBookingConfirmationHTML(booking: any, user: any, meetingRoom: any): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Booking Confirmation</h2>
        <p>Hello ${user.firstName},</p>
        <p>Your booking has been confirmed!</p>
        
        <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
          <h3>${booking.title}</h3>
          <p><strong>Room:</strong> ${meetingRoom.name}</p>
          <p><strong>Location:</strong> ${meetingRoom.location.floor}, ${meetingRoom.location.room}</p>
          <p><strong>Date:</strong> ${new Date(booking.startDate).toLocaleDateString()}</p>
          <p><strong>Time:</strong> ${new Date(booking.startDate).toLocaleTimeString()} - ${new Date(booking.endDate).toLocaleTimeString()}</p>
          ${booking.description ? `<p><strong>Description:</strong> ${booking.description}</p>` : ''}
        </div>
        
        <p>Please arrive on time and ensure the room is clean after your meeting.</p>
        <p>Best regards,<br>Office Management Team</p>
      </div>
    `;
  }

  private generateBookingReminderHTML(booking: any, user: any, meetingRoom: any): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Meeting Reminder</h2>
        <p>Hello ${user.firstName},</p>
        <p>This is a reminder that your meeting starts soon!</p>
        
        <div style="background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
          <h3>${booking.title}</h3>
          <p><strong>Room:</strong> ${meetingRoom.name}</p>
          <p><strong>Location:</strong> ${meetingRoom.location.floor}, ${meetingRoom.location.room}</p>
          <p><strong>Time:</strong> ${new Date(booking.startDate).toLocaleTimeString()} - ${new Date(booking.endDate).toLocaleTimeString()}</p>
        </div>
        
        <p>Don't forget to prepare any materials you might need!</p>
        <p>Best regards,<br>Office Management Team</p>
      </div>
    `;
  }

  private generateBookingCancellationHTML(booking: any, user: any, meetingRoom: any): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Booking Cancelled</h2>
        <p>Hello ${user.firstName},</p>
        <p>Your booking has been cancelled.</p>
        
        <div style="background-color: #f8d7da; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #dc3545;">
          <h3>${booking.title}</h3>
          <p><strong>Room:</strong> ${meetingRoom.name}</p>
          <p><strong>Date:</strong> ${new Date(booking.startDate).toLocaleDateString()}</p>
          <p><strong>Time:</strong> ${new Date(booking.startDate).toLocaleTimeString()} - ${new Date(booking.endDate).toLocaleTimeString()}</p>
          ${booking.cancellationReason ? `<p><strong>Reason:</strong> ${booking.cancellationReason}</p>` : ''}
        </div>
        
        <p>The room is now available for other bookings.</p>
        <p>Best regards,<br>Office Management Team</p>
      </div>
    `;
  }

  private generateGuestInvitationHTML(guest: any, host: any): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>You're Invited!</h2>
        <p>Hello ${guest.name},</p>
        <p>${host.firstName} ${host.lastName} has invited you to visit our office.</p>
        
        <div style="background-color: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745;">
          <p><strong>Visit Date:</strong> ${new Date(guest.visitDate).toLocaleDateString()}</p>
          <p><strong>Time:</strong> ${guest.visitTime.start}${guest.visitTime.end ? ` - ${guest.visitTime.end}` : ''}</p>
          <p><strong>Purpose:</strong> ${guest.purpose}</p>
          <p><strong>Host:</strong> ${host.firstName} ${host.lastName}</p>
          <p><strong>Department:</strong> ${host.department}</p>
        </div>
        
        ${guest.visitInstructions ? `<p><strong>Instructions:</strong> ${guest.visitInstructions}</p>` : ''}
        
        <p>Please bring a valid ID for security purposes.</p>
        <p>We look forward to your visit!</p>
        <p>Best regards,<br>Office Management Team</p>
      </div>
    `;
  }

  private generateGuestArrivalHTML(guest: any, host: any): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Guest Arrival Notification</h2>
        <p>Hello ${host.firstName},</p>
        <p>Your guest has arrived and is waiting for you at the reception.</p>
        
        <div style="background-color: #d1ecf1; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #17a2b8;">
          <p><strong>Guest:</strong> ${guest.name}</p>
          ${guest.company ? `<p><strong>Company:</strong> ${guest.company}</p>` : ''}
          <p><strong>Arrival Time:</strong> ${new Date().toLocaleTimeString()}</p>
          <p><strong>Purpose:</strong> ${guest.purpose}</p>
        </div>
        
        <p>Please come to the reception to meet your guest.</p>
        <p>Best regards,<br>Reception Team</p>
      </div>
    `;
  }
}
