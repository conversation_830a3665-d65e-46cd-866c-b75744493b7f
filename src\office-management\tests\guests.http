### Office Management - Guests API Tests

@baseUrl = http://localhost:3000
@authToken = your-jwt-token-here

### Create a new guest
POST {{baseUrl}}/api/office-management/guests
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "company": "External Corp",
  "visitDate": "2024-01-15",
  "visitTime": {
    "start": "14:00",
    "end": "16:00"
  },
  "purpose": "Business meeting",
  "visitInstructions": "Please wait at reception",
  "identificationRequired": true,
  "accessLevel": "floor",
  "specialRequirements": ["wheelchair access"]
}

### Get all guests for current user (as host)
GET {{baseUrl}}/api/office-management/guests
Authorization: Bearer {{authToken}}

### Get guests with filters
GET {{baseUrl}}/api/office-management/guests?visitDate=2024-01-15&status=confirmed
Authorization: Bearer {{authToken}}

### Get specific guest
GET {{baseUrl}}/api/office-management/guests/60f7b3b3b3b3b3b3b3b3b3b6
Authorization: Bearer {{authToken}}

### Update guest information
PUT {{baseUrl}}/api/office-management/guests/60f7b3b3b3b3b3b3b3b3b3b6
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "visitTime": {
    "start": "15:00",
    "end": "17:00"
  },
  "purpose": "Updated business meeting",
  "visitInstructions": "Please wait at main reception on ground floor"
}

### Confirm guest visit
PATCH {{baseUrl}}/api/office-management/guests/60f7b3b3b3b3b3b3b3b3b3b6/confirm
Authorization: Bearer {{authToken}}

### Check-in guest
PATCH {{baseUrl}}/api/office-management/guests/60f7b3b3b3b3b3b3b3b3b3b6/checkin
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "identificationNumber": "ID123456789",
  "notes": "Guest arrived on time"
}

### Check-out guest
PATCH {{baseUrl}}/api/office-management/guests/60f7b3b3b3b3b3b3b3b3b3b6/checkout
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "notes": "Meeting completed successfully"
}

### Cancel guest visit
PATCH {{baseUrl}}/api/office-management/guests/60f7b3b3b3b3b3b3b3b3b3b6/cancel
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "reason": "Meeting postponed"
}

### Mark guest as no-show
PATCH {{baseUrl}}/api/office-management/guests/60f7b3b3b3b3b3b3b3b3b3b6/no-show
Authorization: Bearer {{authToken}}

### Send guest invitation
POST {{baseUrl}}/api/office-management/guests/60f7b3b3b3b3b3b3b3b3b3b6/invite
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "channels": ["email", "whatsapp"]
}

### Send guest reminder
POST {{baseUrl}}/api/office-management/guests/60f7b3b3b3b3b3b3b3b3b3b6/remind
Authorization: Bearer {{authToken}}

### Get today's expected guests (for reception)
GET {{baseUrl}}/api/office-management/guests/today
Authorization: Bearer {{authToken}}

### Get guest visit history
GET {{baseUrl}}/api/office-management/guests/60f7b3b3b3b3b3b3b3b3b3b6/history
Authorization: Bearer {{authToken}}

### Search guests by name or company
GET {{baseUrl}}/api/office-management/guests/search?q=John&type=name
Authorization: Bearer {{authToken}}

### Get guest statistics
GET {{baseUrl}}/api/office-management/guests/stats?period=month
Authorization: Bearer {{authToken}}

### Bulk invite guests
POST {{baseUrl}}/api/office-management/guests/bulk-invite
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "guests": [
    {
      "name": "Jane Smith",
      "email": "<EMAIL>",
      "company": "Partner Corp",
      "visitDate": "2024-01-16",
      "visitTime": {
        "start": "10:00",
        "end": "12:00"
      },
      "purpose": "Partnership discussion"
    },
    {
      "name": "Bob Johnson",
      "email": "<EMAIL>",
      "company": "Vendor Inc",
      "visitDate": "2024-01-16",
      "visitTime": {
        "start": "14:00",
        "end": "15:30"
      },
      "purpose": "Vendor meeting"
    }
  ],
  "commonSettings": {
    "accessLevel": "lobby",
    "identificationRequired": true
  }
}

### Export guest list
GET {{baseUrl}}/api/office-management/guests/export?format=csv&startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer {{authToken}}

### Delete guest
DELETE {{baseUrl}}/api/office-management/guests/60f7b3b3b3b3b3b3b3b3b3b6
Authorization: Bearer {{authToken}}

### Get guest QR code for check-in
GET {{baseUrl}}/api/office-management/guests/60f7b3b3b3b3b3b3b3b3b3b6/qr-code
Authorization: Bearer {{authToken}}

### Verify guest by QR code
POST {{baseUrl}}/api/office-management/guests/verify-qr
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "qrCode": "encrypted-guest-data-here"
}
