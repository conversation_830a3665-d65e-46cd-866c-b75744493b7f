import { logger } from '@/clean/lib/logger';
import { ANTHROPIC_MODEL } from '@/constants';
import Anthropic from '@anthropic-ai/sdk';

// Define allowed MIME types
const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'] as const;
type AllowedMimeType = (typeof allowedMimeTypes)[number]; // "image/jpeg" | "image/png" | "image/gif" | "image/webp"

type Source = {
  data: string;
  media_type: AllowedMimeType;
  prompt: string;
};

// Initialize Anthropic client
const client = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY, // This is the default and can be omitted
});

const parseImageText = async (param: Source) => {
  logger.info(`[parseImageText] - Source: ${JSON.stringify(param)}`);

  // Validate MIME type
  if (!allowedMimeTypes.includes(param.media_type)) {
    throw new Error(`Unsupported MIME type: ${param.media_type}`);
  }

  try {
    // Send request to Anthropic API
    const response = await client.messages.create({
      model: ANTHROPIC_MODEL,
      max_tokens: 1024,
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'image',
              source: {
                type: 'base64',
                media_type: param.media_type,
                data: param.data,
              },
            },
            { type: 'text', text: param.prompt },
          ],
        },
      ],
    });

    logger.info(`[parseImageText] - Response: ${JSON.stringify(response)}`);

    // Validate and parse response
    if (response?.content?.length > 0) {
      const firstContentBlock = response.content[0];
      if (firstContentBlock.type === 'text') {
        return JSON.parse(firstContentBlock.text);
      } else {
        throw new Error('Unexpected response content type from Anthropic API');
      }
    } else {
      throw new Error('Empty response from Anthropic API');
    }
  } catch (error) {
    logger.error(`[parseImageText] - Error: ${JSON.stringify(error)}`);
    throw new Error(`Failed to parse image text: ${JSON.stringify(error)}`);
  }
};

export { parseImageText, Source, AllowedMimeType };
