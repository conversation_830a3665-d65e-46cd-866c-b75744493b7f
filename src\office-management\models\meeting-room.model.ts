import mongoose, { Schema, Document } from 'mongoose';
import officeManagementDB from '../db';

export interface IMeetingRoom extends Document {
  name: string;
  location: {
    building?: string;
    floor: string;
    room: string;
    address?: string;
  };
  capacity: number;
  amenities: string[];
  equipment: string[];
  description?: string;
  isActive: boolean;
  images?: string[];
  bookingRules: {
    minBookingDuration: number; // in minutes
    maxBookingDuration: number; // in minutes
    advanceBookingDays: number; // how many days in advance can be booked
    minNoticeHours: number; // minimum hours notice required
  };
  availability: {
    businessHours: {
      start: string; // "08:00"
      end: string;   // "18:00"
    };
    workingDays: number[]; // [1,2,3,4,5] for Monday-Friday
    timezone: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

const MeetingRoomSchema = new Schema<IMeetingRoom>({
  name: {
    type: String,
    required: true,
    trim: true,
    unique: true
  },
  location: {
    building: {
      type: String,
      trim: true
    },
    floor: {
      type: String,
      required: true,
      trim: true
    },
    room: {
      type: String,
      required: true,
      trim: true
    },
    address: {
      type: String,
      trim: true
    }
  },
  capacity: {
    type: Number,
    required: true,
    min: 1,
    max: 100
  },
  amenities: [{
    type: String,
    trim: true
  }],
  equipment: [{
    type: String,
    trim: true
  }],
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  isActive: {
    type: Boolean,
    default: true
  },
  images: [{
    type: String
  }],
  bookingRules: {
    minBookingDuration: {
      type: Number,
      default: 60, // 1 hour minimum
      min: 30
    },
    maxBookingDuration: {
      type: Number,
      default: 480, // 8 hours maximum
      max: 480
    },
    advanceBookingDays: {
      type: Number,
      default: 30,
      min: 1,
      max: 90
    },
    minNoticeHours: {
      type: Number,
      default: 1,
      min: 0,
      max: 24
    }
  },
  availability: {
    businessHours: {
      start: {
        type: String,
        default: "08:00",
        validate: {
          validator: function (time: string) {
            return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
          },
          message: 'Invalid time format. Use HH:MM'
        }
      },
      end: {
        type: String,
        default: "18:00",
        validate: {
          validator: function (time: string) {
            return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
          },
          message: 'Invalid time format. Use HH:MM'
        }
      }
    },
    workingDays: {
      type: [Number],
      default: [1, 2, 3, 4, 5], // Monday to Friday
      validate: {
        validator: function (days: number[]) {
          return days.every(day => day >= 0 && day <= 6);
        },
        message: 'Working days must be between 0 (Sunday) and 6 (Saturday)'
      }
    },
    timezone: {
      type: String,
      default: "America/Mexico_City"
    }
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function (doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes
MeetingRoomSchema.index({ name: 1 });
MeetingRoomSchema.index({ isActive: 1 });
MeetingRoomSchema.index({ capacity: 1 });
MeetingRoomSchema.index({ 'location.floor': 1 });

export const MeetingRoomModel = officeManagementDB.model<IMeetingRoom>('MeetingRoom', MeetingRoomSchema);
