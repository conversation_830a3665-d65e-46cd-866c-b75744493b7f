{"name": "onecarnow-backendv2", "version": "1.1.17", "main": "index.js", "engines": {"node": ">=20.0.0 <=22.x", "npm": ">=10.0.0"}, "scripts": {"dev": "tsx watch ./src/index.ts", "start": "node build/index.js", "tsc": "tsc", "test": "jest --runInBand --forceExit --testTimeout=30000", "lint": "eslint --ext .ts src", "lint:fix": "eslint --fix --ext .ts src", "prettier": "prettier --write \"src/**/*.ts\"", "prettier:check": "prettier --check \"src/**/*.ts\"", "prettier:write": "prettier --write \"src/**/*.ts\"", "build": "tsc && npx tsc-alias --outDir build --verbose && node fix-imports.mjs", "build-2": "tsc && tsc-alias --outDir build --verbose && node fix-imports.mjs", "clean": "tsc --build --clean", "prepare": "husky install"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@faker-js/faker": "8.4.1", "@jest/globals": "29.5.0", "@types/bcrypt": "5.0.0", "@types/cors": "2.8.13", "@types/express": "4.17.17", "@types/jest": "29.5.1", "@types/jsonwebtoken": "9.0.2", "@types/lodash": "^4.17.17", "@types/morgan": "1.9.4", "@types/multer": "1.4.7", "@types/node": "^22.10.5", "@types/nodemailer": "6.4.7", "@types/qrcode": "^1.5.5", "@types/supertest": "2.0.12", "@types/uuid": "9.0.7", "@typescript-eslint/eslint-plugin": "7.1.0", "@typescript-eslint/parser": "7.1.0", "axios-mock-adapter": "2.0.0", "eslint": "8.57.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.6", "husky": "8.0.0", "prettier": "^3.5.3", "replace-in-file": "8.2.0", "supertest": "6.3.3", "ts-jest": "29.1.0", "tsc-alias": "1.8.10", "tsconfig-paths": "4.2.0", "tsx": "^4.19.2", "typescript": "5.0.4"}, "dependencies": {"@anthropic-ai/sdk": "^0.33.1", "@aws-sdk/client-eventbridge": "^3.744.0", "@aws-sdk/client-s3": "3.344.0", "@aws-sdk/client-scheduler": "^3.744.0", "@aws-sdk/s3-request-presigner": "3.344.0", "@google-cloud/documentai": "8.7.0", "@google/generative-ai": "0.24.0", "@nodecfdi/cfdi-to-json": "^2.0.1", "@slack/web-api": "^7.9.1", "@supabase/supabase-js": "2.47.12", "@types/luxon": "3.4.2", "@types/node-cron": "3.0.11", "@xmldom/xmldom": "^0.9.8", "async-hooks": "^1.3.1", "axios": "1.4.0", "bcrypt": "^6.0.0", "body-parser": "^1.20.3", "bullmq": "5.12.5", "cheerio": "^1.0.0", "class-transformer": "0.5.1", "cors": "2.8.5", "date-fns": "2.30.0", "dotenv": "16.0.3", "express": "4.18.2", "express-validator": "7.0.1", "firebase-admin": "^13.4.0", "form-data": "^4.0.1", "google-auth-library": "9.14.2", "googleapis": "^144.0.0", "http-status": "^2.1.0", "https-proxy-agent": "^7.0.6", "jsonwebtoken": "9.0.0", "lodash": "^4.17.21", "luxon": "3.5.0", "moment": "2.29.4", "moment-timezone": "0.5.43", "mongodb-memory-server": "8.12.2", "mongoose": "6.11.0", "morgan": "1.10.0", "multer": "1.4.5-lts.1", "node-cron": "3.0.3", "nodemailer": "6.9.1", "pdf-lib": "^1.17.1", "puppeteer": "^24.6.1", "qrcode": "^1.5.4", "sharp": "^0.34.1", "twilio": "^5.5.1", "uuid": "9.0.1", "winston": "3.14.1", "winston-daily-rotate-file": "5.0.0", "xlsx": "0.18.5", "zod": "3.23.8"}}