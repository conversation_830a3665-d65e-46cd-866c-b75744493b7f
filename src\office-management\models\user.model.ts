import mongoose, { Schema, Document } from 'mongoose';
import officeManagementDB from '../db';

export interface IOfficeUser extends Document {
  employeeId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  department: string;
  position: string;
  manager?: mongoose.Types.ObjectId;
  officeLocation: {
    building?: string;
    floor: string;
    desk?: string;
  };
  workSchedule: {
    workingDays: number[]; // [1,2,3,4,5] for Monday-Friday
    startTime: string; // "09:00"
    endTime: string;   // "18:00"
    timezone: string;
  };
  permissions: {
    canBookMeetingRooms: boolean;
    canBookDesks: boolean;
    canManageGuests: boolean;
    canViewReports: boolean;
    isAdmin: boolean;
    maxBookingDuration: number; // in hours
    maxAdvanceBookingDays: number;
  };
  preferences: {
    defaultMeetingDuration: number; // in minutes
    notificationSettings: {
      email: boolean;
      slack: boolean;
      whatsapp: boolean;
    };
    favoriteRooms: mongoose.Types.ObjectId[];
  };
  slackUserId?: string;
  profilePicture?: string;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const OfficeUserSchema = new Schema<IOfficeUser>({
  employeeId: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    uppercase: true
  },
  firstName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },
  lastName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    validate: {
      validator: function(email: string) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
      },
      message: 'Invalid email format'
    }
  },
  phone: {
    type: String,
    trim: true,
    validate: {
      validator: function(phone: string) {
        return !phone || /^\+?[\d\s\-\(\)]+$/.test(phone);
      },
      message: 'Invalid phone format'
    }
  },
  department: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  position: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  manager: {
    type: Schema.Types.ObjectId,
    ref: 'OfficeUser'
  },
  officeLocation: {
    building: {
      type: String,
      trim: true
    },
    floor: {
      type: String,
      required: true,
      trim: true
    },
    desk: {
      type: String,
      trim: true
    }
  },
  workSchedule: {
    workingDays: {
      type: [Number],
      default: [1, 2, 3, 4, 5], // Monday to Friday
      validate: {
        validator: function(days: number[]) {
          return days.every(day => day >= 0 && day <= 6);
        },
        message: 'Working days must be between 0 (Sunday) and 6 (Saturday)'
      }
    },
    startTime: {
      type: String,
      default: "09:00",
      validate: {
        validator: function(time: string) {
          return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
        },
        message: 'Invalid time format. Use HH:MM'
      }
    },
    endTime: {
      type: String,
      default: "18:00",
      validate: {
        validator: function(time: string) {
          return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
        },
        message: 'Invalid time format. Use HH:MM'
      }
    },
    timezone: {
      type: String,
      default: "America/Mexico_City"
    }
  },
  permissions: {
    canBookMeetingRooms: {
      type: Boolean,
      default: true
    },
    canBookDesks: {
      type: Boolean,
      default: true
    },
    canManageGuests: {
      type: Boolean,
      default: false
    },
    canViewReports: {
      type: Boolean,
      default: false
    },
    isAdmin: {
      type: Boolean,
      default: false
    },
    maxBookingDuration: {
      type: Number,
      default: 8, // 8 hours
      min: 1,
      max: 24
    },
    maxAdvanceBookingDays: {
      type: Number,
      default: 30,
      min: 1,
      max: 90
    }
  },
  preferences: {
    defaultMeetingDuration: {
      type: Number,
      default: 60, // 1 hour
      min: 30,
      max: 480
    },
    notificationSettings: {
      email: {
        type: Boolean,
        default: true
      },
      slack: {
        type: Boolean,
        default: true
      },
      whatsapp: {
        type: Boolean,
        default: false
      }
    },
    favoriteRooms: [{
      type: Schema.Types.ObjectId,
      ref: 'MeetingRoom'
    }]
  },
  slackUserId: {
    type: String,
    trim: true
  },
  profilePicture: {
    type: String,
    validate: {
      validator: function(url: string) {
        return !url || /^https?:\/\/.+\.(jpg|jpeg|png|gif)$/i.test(url);
      },
      message: 'Invalid profile picture URL format'
    }
  },
  emergencyContact: {
    name: {
      type: String,
      trim: true,
      maxlength: 100
    },
    phone: {
      type: String,
      trim: true
    },
    relationship: {
      type: String,
      trim: true,
      maxlength: 50
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastLogin: Date
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes
OfficeUserSchema.index({ employeeId: 1 });
OfficeUserSchema.index({ email: 1 });
OfficeUserSchema.index({ department: 1 });
OfficeUserSchema.index({ isActive: 1 });
OfficeUserSchema.index({ manager: 1 });

// Virtual for full name
OfficeUserSchema.virtual('fullName').get(function(this: IOfficeUser) {
  return `${this.firstName} ${this.lastName}`;
});

export const OfficeUserModel = officeManagementDB.model<IOfficeUser>('OfficeUser', OfficeUserSchema);
