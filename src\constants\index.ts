import * as dotenv from 'dotenv';
dotenv.config();

const root = process.cwd();

dotenv.config({ path: `${root}/.env.${process.env.NODE_ENV || 'development'}` });

export const MONGODB_URI = process.env.MONGODB_URI as string;

export const isProd = process.env.NODE_ENV === 'production';
// export const isDev = process.env.NODE_ENV === 'development' || process.env.IS_DEV;
export const isDev = !!process.env.IS_DEV || process.env.NODE_ENV === 'development';

//onboarding support
export const ONBOARDING_SUPPORT_URL = process.env.ONBOARDING_SUPPORT_URL as string;

//otp api
export const OTP_EXPIRY = process.env.OTP_EXPIRY as unknown as number;

// ocr api
export const MAX_UPLOAD_REQUESTS = process.env.MAX_UPLOAD_REQUESTS as unknown as number;
export const ANTHROPIC_API_KEY = process.env.ANTHROPIC_API_KEY as string;
export const ANTHROPIC_MODEL = process.env.ANTHROPIC_MODEL as string;
export const ANTHROPIC_TOKEN_LIMIT = process.env.ANTHROPIC_TOKEN_LIMIT as unknown as number;

export const GEMINI_API_KEY = process.env.GEMINI_API_KEY as string;
export const GEMINI_MODEL = process.env.GEMINI_MODEL as string;
export const GEMINI_BACKOFF_MODEL = 'gemini-2.0-flash-lite';
export const GEMINI_MAX_OUTPUT_TOKENS = process.env.GEMINI_MAX_OUTPUT_TOKENS as unknown as number;

// jwt secrets
export const accessTokenSecret = process.env.ACCESS_TOKEN_SECRET as string;
export const recoverPasswordSecret = process.env.RECOVER_PASSWORD_SECRET as string;
export const invitationSecret = process.env.INVITATION_SECRET as string;
export const externalAccessTokenSecret = process.env.EXTERNAL_ACCESS_TOKEN_SECRET as string;
export const cronJobAccessTokenSecret = process.env.CRON_JOB_TOKEN_SECRET as string;

export const cronPassword = process.env.CRON_PASSWORD as string;

// email sender
export const emailSender = process.env.EMAIL_SENDER as string;
export const emailSenderPassword = process.env.EMAIL_SENDER_PASSWORD as string;
export const PORT = 3000;

export const SMTP_HOST = process.env.SMTP_HOST as string;
export const SMTP_USER = process.env.SMTP_USER as string;
export const SMTP_PASS = process.env.SMTP_PASS as string;

// AWS
export const AWS_BUCKET_NAME = process.env.AWS_BUCKET_NAME as string;
export const AWS_BUCKET_REGION = process.env.AWS_BUCKET_REGION || 'us-east-1';
// export const AWS_BUCKET_SECRET_KEY =
//   process.env.AWS_BUCKET_SECRET_KEY || 'MLrwdhVl2ElkrBR+mSN+Vd31od8Iko3kYzelhtpd';
// export const AWS_BUCKET_PUBLIC_KEY = process.env.AWS_BUCKET_PUBLIC_KEY || '********************';

export const AWS_BUCKET_PUBLIC_KEY = (process.env.AWS_BUCKET_PUBLIC_KEY as string) || '********************';
export const AWS_BUCKET_SECRET_KEY =
  (process.env.AWS_BUCKET_SECRET_KEY as string) || 'NPXtBKuxHGWD1yP39OthwDftIJzgMPZyX8im/8pW';

// Google
export const GOOGLE_PROJECT = process.env.GOOGLE_PROJECT as string;
export const GOOGLE_LOCATION = process.env.GOOGLE_LOCATION as string;
export const GOOGLE_PROCESSOR = process.env.GOOGLE_PROCESSOR as string;

//GigStack

export const GIGSTACK_URL = 'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1';

export const GIGSTACK_TOKEN = process.env.GIGSTACK_TOKEN as string;
export const CDMX_GIGSTACK_TOKEN = process.env.CDMX_GIGSTACK_TOKEN as string;
export const QRO_GIGSTACK_TOKEN = process.env.QRO_GIGSTACK_TOKEN as string;
export const GDL_GIGSTACK_TOKEN = process.env.GDL_GIGSTACK_TOKEN as string;
export const TIJ_GIGSTACK_TOKEN = process.env.TIJ_GIGSTACK_TOKEN as string;
export const MTY_GIGSTACK_TOKEN = process.env.MTY_GIGSTACK_TOKEN as string;
export const PUE_GIGSTACK_TOKEN = process.env.PBC_GIGSTACK_TOKEN as string;
export const PBC_GIGSTACK_TOKEN = process.env.PBC_GIGSTACK_TOKEN as string;
export const PBE_GIGSTACK_TOKEN = process.env.PBE_GIGSTACK_TOKEN as string;
export const TOL_GIGSTACK_TOKEN = process.env.TOL_GIGSTACK_TOKEN as string;
export const PTV_GIGSTACK_TOKEN = process.env.PTV_GIGSTACK_TOKEN as string;
export const TEP_GIGSTACK_TOKEN = process.env.TEP_GIGSTACK_TOKEN as string;
export const COL_GIGSTACK_TOKEN = process.env.COL_GIGSTACK_TOKEN as string;
export const SAL_GIGSTACK_TOKEN = process.env.SAL_GIGSTACK_TOKEN as string;
export const TORR_GIGSTACK_TOKEN = process.env.TORR_GIGSTACK_TOKEN as string;
export const DUR_GIGSTACK_TOKEN = process.env.DUR_GIGSTACK_TOKEN as string;
export const MXLI_GIGSTACK_TOKEN = process.env.MXLI_GIGSTACK_TOKEN as string;
export const HER_GIGSTACK_TOKEN = process.env.HER_GIGSTACK_TOKEN as string;
export const CHI_GIGSTACK_TOKEN = process.env.CHI_GIGSTACK_TOKEN as string;
export const LEO_GIGSTACK_TOKEN = process.env.LEO_GIGSTACK_TOKEN as string;
export const AGS_GIGSTACK_TOKEN = process.env.AGS_GIGSTACK_TOKEN as string;
export const SLP_GIGSTACK_TOKEN = process.env.SLP_GIGSTACK_TOKEN as string;
export const MER_GIGSTACK_TOKEN = process.env.MER_GIGSTACK_TOKEN as string;

//Rasayel token

export const RASAYEL_TOKEN = process.env.RASAYEL_BASIC_TOKEN as string;

// HUBSPOT

export const HUBSPOT_TOKEN = process.env.HUBSPOT_TOKEN as string;
export const HUBSPOT_URL = 'https://api.hubapi.com/crm/v3/objects/contacts?hapikey';

// HILOS
export const HILOS_URL = process.env.HILOS_URL as string;
export const HILOS_URL_SEND_TEMPLATE = process.env.HILOS_URL_SEND_TEMPLATE as string;
export const HILOS_API_KEY = process.env.HILOS_API_KEY as string;
export const HILOS_API = 'https://api.hilos.io/api';
export const HILOS_TEMPLATE_ID = process.env.HILOS_TEMPLATE_ID as string;

// STP

export const STP_PASSPHRASE = process.env.STP_PASSPHRASE as string;
export const STP_PRIVATE_KEY = process.env.STP_PRIVATE_KEY as string;
export const STP_ORDER_REGISTER_URL = process.env.STP_ORDER_REGISTER_URL as string;
export const STP_CONCILIATION_URL = process.env.STP_CONCILIATION_URL as string;
export const STP_ACCOUNT_BALANCE_URL = process.env.STP_ACCOUNT_BALANCE_URL as string;
export const STP_MAIN_ACCOUNT = process.env.STP_MAIN_ACCOUNT as string;

//REQUEST URL
export const REQUEST_URL = process.env.REQUEST_URL as string;

// WIRE4
export const WIRE4_TOKEN_URL = process.env.WIRE4_TOKEN_URL as string;
export const WIRE4_CLIENT_ID = process.env.WIRE4_CLIENT_ID as string;
export const WIRE4_CLIENT_SECRET = process.env.WIRE4_CLIENT_SECRET as string;
export const WIRE4_I80_CLIENT_ID = process.env.WIRE4_i80_CLIENT_ID as string;
export const WIRE4_I80_CLIENT_SECRET = process.env.WIRE4_i80_CLIENT_SECRET as string;

export const WIRE4_ACCOUNTS_URL = process.env.WIRE4_ACCOUNTS_URL as string;
export const WIRE4_API_URL = process.env.WIRE4_API_URL as string;
export const WIRE4_SUCRIPTION = process.env.WIRE4_SUCRIPTION as string;
export const WIRE4_OLD_SUCRIPTION = process.env.WIRE4_OLD_SUCRIPTION as string;
export const WIRE4_I80_SUCRIPTION = process.env.WIRE4_i80_SUCRIPTION as string;

export const WIRE4_API_USERNAME = process.env.WIRE4_API_USERNAME as string;
export const WIRE4_OLD_API_USERNAME = process.env.WIRE4_OLD_API_USERNAME as string;
export const WIRE4_I80_API_USERNAME = process.env.WIRE4_i80_API_USERNAME as string;

export const WIRE4_API_PASSWORD = process.env.WIRE4_API_PASSWORD as string;
export const WIRE4_OLD_API_PASSWORD = process.env.WIRE4_OLD_API_PASSWORD as string;
export const WIRE4_I80_API_PASSWORD = process.env.WIRE4_i80_API_PASSWORD as string;

// GPS
export const GPS_API_TOKEN = process.env.GPS_API_TOKEN as string;
export const GPS_API_URL = process.env.GPS_API_URL as string;
export const GPS_API_USERNAME = process.env.GPS_API_USERNAME as string;
export const GPS_API_PASSWORD = process.env.GPS_API_PASSWORD as string;
export const GPS_API_EVENTS = process.env.GPS_API_EVENTS as string;

// KEY TO GET VEHICLES FOR WEBSITE:

export const VEHICLES_KEY = process.env.VEHICLES_KEY as string;

// Slack XML Bot
export const SLACK_NOTIFIER_BOT_TOKEN = process.env.SLACK_NOTIFIER_BOT_TOKEN as string;
export const SLACK_BULK_UPLOAD_CHANNEL_ID = process.env.SLACK_BULK_UPLOAD_CHANNEL_ID as string;
export const SLACK_MAINTENANCE_CHANNEL_ID = process.env.SLACK_MAINTENANCE_CHANNEL_ID as string;
export const SLACK_VEHICLE_DOC_UPLOAD_CHANNEL_ID =
  process.env.SLACK_VEHICLE_DOC_UPLOAD_CHANNEL_ID || 'DEFAULT_CHANNEL_ID'; // TODO: Replace with actual default or ensure ENV var is set
export const XML_NOTIFICATION_EMAILS = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

export const FRONTEND_ADMIN_URL = process.env.FRONTEND_ADMIN_URL;
export const VENDOR_PANEL_URL = process.env.VENDOR_PANEL_URL;

export const slackTexts = {
  noFilesMessage: 'No se cargaron archivos para procesar',
  noFlesBottomText: 'No se encontraron archivos para procesar',
  fileProcessErrorBottomText: 'Se produjo un error durante el procesamiento de los archivos',
  bulkUploadErrorBottomText: 'Se produjo un error en el punto de carga masiva',

  fileErrorBottomText: (errorCount: number) =>
    `Se encontraron errores en ${errorCount} archivos. Ver detalles adjuntos.`,
  fileSuccessBottomText: (successCount: number, filesNumber: number) =>
    `Todos los archivos se procesaron correctamente (${successCount}/${filesNumber})`,

  uploadError: (errorMessage: string) => `Error en el punto de carga masiva: ${errorMessage}`,

  summaryTitle: (userName: string) =>
    `Hola ${userName},\n Esperamos que este mensaje le encuentre bien. Le informamos que el sistema ha procesado recientemente una carga masiva de vehículos en nuestra plataforma. A continuación, encontrará el resumen detallado de esta operación.`,

  summaryMessage: (
    filesNumber: number,
    successCount: number,
    errorCount: number
  ) => `Resumen de carga de archivos: 
  Total de archivos: ${filesNumber}
  Exitosos: ${successCount}
  Fallidos: ${errorCount}`,

  criticalErrorBottomText: 'Se produjo un error crítico durante el procesamiento',
  criticalError: (errorMessage: string) => `Error crítico durante el procesamiento del XML: ${errorMessage}`,

  processComplete: '✅ Carga de XML Exitosa',
  processCompleteWithFailure: '⚠️ Error en Carga de XML',
  criticalErrorTitle: '⚠️ Error Crítico',
  processFailedTitle: '⚠️ Error en Procesamiento',
  bulkUploadFailedTitle: '⚠️ Error en Carga Masiva',
};

export const vehicleDocumentsSlackTexts = {
  ...slackTexts,
  // Overrides for vehicle documents
  noFilesMessage: 'No se cargaron documentos para procesar',
  noFlesBottomText: 'No se encontraron documentos para procesar',
  fileProcessErrorBottomText: 'Se produjo un error durante el procesamiento de los documentos',
  bulkUploadErrorBottomText: 'Se produjo un error en la carga de documentos',

  fileErrorBottomText: (errorCount: number) =>
    `Se encontraron errores en ${errorCount} documentos. Ver detalles adjuntos.`,
  fileSuccessBottomText: (successCount: number, filesNumber: number) =>
    `Todos los documentos se procesaron correctamente (${successCount}/${filesNumber})`,

  uploadError: (errorMessage: string) => `Error en la carga de documentos: ${errorMessage}`,

  summaryTitle: (userName: string) =>
    `Hola ${userName},\nLe informamos sobre el estado del procesamiento de los documentos de vehículos. A continuación, encontrará el resumen detallado:`,

  summaryMessage: (
    filesNumber: number,
    successCount: number,
    errorCount: number
  ) => `Resumen del procesamiento de documentos: 
  Total de documentos: ${filesNumber}
  Procesados correctamente: ${successCount}
  Con errores: ${errorCount}`,

  criticalErrorBottomText: 'Se produjo un error crítico durante el procesamiento de documentos',
  criticalError: (errorMessage: string) =>
    `Error crítico durante el procesamiento de documentos: ${errorMessage}`,

  processComplete: '✅ Todos los documentos de vehículo se procesaron correctamente',
  processCompleteWithFailure: '⚠️ Error en el procesamiento de algunos documentos de vehículo',
  processFailedTitle: '⚠️ Error en Procesamiento de Documentos',
  bulkUploadFailedTitle: '⚠️ Error en Carga de Documentos',
};

export const mongoErroCodes = {
  DUPLICATE_KEY: 11000,
};

// social scoring
export const MIN_EARNINGS_APPROVED = 7000;
export const MIN_EARNINGS_APPROVED_WITH_CONDITIONS = 6000;
export const MAX_EARNINGS_APPROVED_WITH_CONDITIONS = 6999;
export const MIN_WEEKS_TO_ANALYZE = 12;
export const DEFAULT_PAGINATION_LIMIT = 10;
export const MAX_PAGINATION_LIMIT = 100;
export enum EarningsAnalysisStatus {
  pending = 'pending',
  approved = 'approved',
  rejected = 'rejected',
  approved_with_conditions = 'approved_with_conditions',
}
export const MAX_PALENCA_WAITING_TIME_MS = 60;
export const PALENCA_WIDGET_ID = '04d4f458-9991-4c44-9464-8786cb9e046e';
export const PALENCA_API_BASE_URL = 'https://api.palenca.com/v1';
export const PALENCA_API_KEY = process.env.PALENCA_API_KEY as string;

// Twilio
export const TWILIO_ACCOUNT_SID = process.env.TWILIO_ACCOUNT_SID as string;
export const TWILIO_AUTH_TOKEN = process.env.TWILIO_AUTH_TOKEN as string;
export const TWILIO_PHONE_NUMBER = process.env.TWILIO_PHONE_NUMBER as string;
export const TWILIO_SERVICE_ID = process.env.TWILIO_SERVICE_ID as string;

export enum GigPlatform {
  uber = 'uber',
  didi = 'didi',
  indriver = 'indriver',
}
export enum CurrencyCode {
  mxn = 'mxn',
  usd = 'usd',
}
export class DailyEarning {
  amount: number;

  countTrips: number;

  earningDate: Date;

  currency: CurrencyCode;

  constructor(props: DailyEarning) {
    this.amount = props.amount;
    this.countTrips = props.countTrips;
    this.earningDate = props.earningDate;
    this.currency = props.currency;
  }
}
export class WeeklyEarning {
  totalAmount: number;

  totalTrips: number;

  fromDate: Date;

  toDate: Date;

  week: number;

  year: number;

  currency: CurrencyCode;

  dailyEarnings: DailyEarning[];

  constructor(props: WeeklyEarning) {
    this.totalAmount = props.totalAmount;
    this.totalTrips = props.totalTrips;
    this.fromDate = props.fromDate;
    this.toDate = props.toDate;
    this.week = props.week;
    this.year = props.year;
    this.currency = props.currency;
    this.dailyEarnings = props.dailyEarnings;
  }
}

export enum AdmissionRequestRejectionReason {
  earnings_analysis = 'Rejected after earnings analysis',
  documents_analysis = 'Rejected after documents analysis',
  judicial_analysis = 'Rejected after judicial analysis',
  risk_analysis = 'Rejected after risk analysis',
  home_visit = 'Rejected after home visit',
  final_evaluation = 'Rejected after final evaluation',
}
export enum PalencaRetrievalStatus {
  pending = 'pending',
  queued = 'queued',
  success = 'success',
  error = 'error',
}
// multer

export const multerArray = [
  {
    name: 'vehiclePhoto',
  },
  {
    name: 'bill',
  },
];

export const displayText = {
  home: {
    title: 'OneCarNow!',
    cv: 'cv',
  },
};

export const stockVehiclesText = {
  errors: {
    missingId: 'Id requerido',
    vehicleNotFound: 'Vehículo no encontrado',
    vehicleAlreadyExists: 'Este vehiculo ya existe!',
    vehicleNotAdded: 'Vehiculo no añadido!',
    vehicleNotUpdated: 'Vehiculo no actualizado!',
    vehicleNotDeleted: 'Vehiculo no eliminado!',
    vehicleNotDeletedFromStock: 'Vehiculo no eliminado en stock!',
    vehicleNotAddedToStock: 'Vehiculo no añadido a stock!',
    vehicleNotUpdatedInStock: 'Vehiculo no actualizado en stock!',
    token: 'Authorization required!',
    stockVehiclesNotFound: 'Vehiculo stock no encontrado!',
    missingBody: 'Faltan propiedades del body',
    error: 'error',
    historyDataMissing: 'History data es requerido!',
    dischargedMissing: 'Motivo y/o fecha de la baja requerido',
  },
  success: {
    contractCreated: 'Contrato creado',
    vehicleAdded: 'Vehículo agregado',
    vehicleUpdated: 'Vehiculo actualizado!',
    vehicleDeleted: 'Vehiculo eliminado!',
    vehicleDeletedFromStock: 'Vehiculo eliminado de stock!',
    vehicleAddedToStock: 'Vehiculo añadido a stock!',
    vehicleUpdatedInStock: 'Vehiculo actualizado en stock!',
    information: 'Información del vehiculo de stock',
    dischargedVehicle: 'Vehiculo enviado a bajas',
  },
  paths: {
    vehiclePhoto: 'stock/photos/',
    bill: 'stock/bills/',
  },
};

export const mainContractResponse = {
  errors: {
    mainContractNotFound: 'Contrato principal no existe',
  },
  success: {
    created: 'Contrato principal creado',
    update: 'Contrato principal actualizado',
  },
};

export const documentsResponse = {
  errors: {
    curpRequired: 'Curp requerida',
    taxStatus: 'Constancia de situación fiscal requerida',
    addressVerification: 'Comprobante de domicilio requerido',
    bankStatements: 'Al menos 1 documento requerido',
  },
};

export const STP_CONSTANTS = {
  empresa: 'ONECARNOW2',
  rfcCurpOrdenante: 'EGM210216UV7',
  typeOrder: {
    send: 'E',
    received: 'R',
  },
  typeStates: ['LIQUIDADO', 'CANCELADO', 'DEVUELTO'],

  errors: {
    missingBody: 'Faltan datos',
    originalString: 'Error al obtener la cadena original',
    createRegister: 'Error al crear el registro',
    conciliation: 'No se encontraron registros',
  },
  success: {
    createRegister: 'Registro creado',
    conciliation: 'Conciliación realizada',
  },
  accounts: {
    base: {
      stp: 646,
      plaza: 180,
      prefijoOcn: 3642,
    },
    preAccount: [6, 4, 6, 1, 8, 0, 3, 6, 4, 2],
    multiply: [3, 7, 1, 3, 7, 1, 3, 7, 1, 3, 7, 1, 3, 7, 1, 3, 7],
    preresult: [18, 28, 6, 3, 56, 0, 9, 42, 4],
    presultModule: [8, 8, 6, 3, 6, 0, 9, 6, 4],
    blacklist: ['646180364200002996'],
  },
};

export const associateText = {
  errors: {
    badRequest: 'Mal llamado, error',
    vehicleNotFound: 'No se encontro el vehiculo seleccionado',
    vehicleActive: 'El vehiculo se encuentra activo',
    associateNotFound: 'No se encontro al asociado',
    associateNotCreated: 'No se pudo crear al asociado',
    notAutorized: 'No autorizado, se requieren permisos adicionales',
    associateNotUpdated: 'No se pudo actualizar la informacion del usuario',
    errorAssociateUnassign: 'No se pudo remover al asociado del vehiculo',
    emailNotFound: 'No se encontro el email',
    personalDataMissing: 'Se requiere la información personal del asociado.',
    avalDataMissing: 'Se necesita información del garante',
    missingDocs: 'Faltan los documentos del asociado.',
    missingFields: 'Faltan los siguientes campos obligatorios:',
    missingFieldsPersonalData: 'Faltan los siguientes campos obligatorios en los datos personales:',
    provideFields: 'Por favor, proporcione los detalles necesarios.',
    invalidCountry: (country: string) =>
      `El país ingresado "${country}" no es válido. Debe ser "MX" (México) o "US" (Estados Unidos).`,
    provideFieldsPersonalData: 'Por favor, complete toda la información requerida.',
    vehicleAndClientCountryIsDifferent: 'Vehicle and client must be from same country',
    clientRegisterationFailed: "Error occured while registering client's account on payment module",
    missingRequestId: 'Falta el ID de solicitud de admisión',
  },
  success: {
    associateUnassigned: 'Conductor removido del vehiculo con exito',
    associateCreated: 'Socio OCN Creado correctamente!',
    getAllAssociates: 'Total de asociados',
    associateUpdated: 'Asociado actualizado con exito',
    emailFind: 'Email encontrado',
  },
};

export const integrations = {
  externalsUsers: {
    enterprices: ['STP'],
    emails: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
  },
  enterprice: {
    STP: {
      routes: ['change-stp-state', 'change-stp-payment'],
    },
  },
};

export const hubspot = {
  succes: {
    created: 'Contacto creado correctamente',
  },
  errors: {
    genericError: 'Hubo un error',
    existingEmail: 'Ya has enviado tu solicitud, espera a que te contactemos',
  },
};

export const associatePaymentsConsts = {
  gigstackValues: {
    availableRegions: [
      'CDMX',
      'QRO',
      'GDL',
      'TIJ',
      'MTY',
      'TEST',
      'PUE',
      'TOL',
      'PUE',
      'TEP',
      'COL',
      'SAL',
      'TOR',
      'DUR',
      'MXL',
      'HER',
      'CHI',
      'LEO',
      'AGS',
      'SLP',
      'MER',
    ],
    regionsByCode: {
      0: 'TEST',
      1: 'CDMX',
      2: 'GDL',
      3: 'MTY',
      4: 'QRO',
      5: 'TIJ',
      6: 'MOKA',
      7: 'PUE',
    },
    TEST: {
      weeklyRentName: 'Renta Semanal',
      rentId: 'service_wLLzJcpFek',
      aditionalPayment: '',
      aditionalPaymentName: '',
      blockId: 'service_kedNoiupQf',
    },
    weeklyRentName: 'Renta Semanal',
    rentId: 'service_wLLzJcpFek',
    aditionalPayment: '',
    aditionalPaymentName: '',
    blockId: 'service_kedNoiupQf',
    successStatus: 'succeeded',
    canceledStatus: 'canceled',
    pending: 'requires_payment_method',
  },
  success: {
    created: 'Pagos del asociado creados con exito',
    paymentCreated: 'Pago de la semana creado con exito',
    paymentUpdated: 'Pago actualizado!',
    blockPayment: 'Generado cargo por reativacion',
    paymentVerification: 'Verificacion de los pagos exitosa',
    historyPaymentAdded: 'Pago agregado al historial correctamente',
    paymentCancelled: 'Pago cancelado y restado del balance',
    otherPayments: 'Pago agregado a OtherPayments, no es de renta ni de bloqueo',
  },
  errors: {
    gigstackExistent: 'Usuario no existente en gigstack',
    existingPaymentTable: 'Ya existe la tabla de pagos, favor de actualizarla',
    existingMonexClabe: 'Asociado ya cuenta con clabe MONEX',
    duplicatedPayment: 'Pago de renta semanal duplicado, agrergado en Other Payments',
    error: 'Error al crear los pagos del asociado',
    mainContract404: 'MainContractId no encontrato',
    payment404: 'PaymentAssociate no encontrato',
    notValidRegion: 'Region no valida',
    userNotInFlow: 'Usuario no cuenta con CLABE monex probablemente no esta dentro del flujo',
    modelNotFound: 'La tabla de pagos no tiene un modelo asignado',
    paymentNotFound: 'Registro de pagos no encontrado',
  },
};

export const genericMessages = {
  errors: {
    unauthorized: 'No autorizado',
    unauthorized_v2: 'El token ya ha sido invalidado anteriormente',
    missingBody: 'Faltan datos',
    missingParams: 'Faltan parametros',
    missingFile: 'Falta el archivo',
    missingEmail: 'Falta el email',
    missingPassword: 'Falta la contraseña',
    missingId: 'Falta el id',
    missingName: 'Falta el nombre',
    missingLastName: 'Falta el apellido',
    missingPhone: 'Falta el telefono',
    missingAddress: 'Falta la direccion',
    missingCity: 'Falta la ciudad',
    missingState: 'Falta el estado',
    missingZipCode: 'Falta el codigo postal',
    missingCountry: 'Falta el pais',
    missingRFC: 'Falta el RFC',
    missingCURP: 'Falta el CURP',
    missingBirthDate: 'Falta la fecha de nacimiento',
    missingGender: 'Falta el genero',
    missingMaritalStatus: 'Falta el estado civil',
    missingNationality: 'Falta la nacionalidad',
    missingPhoto: 'Falta la foto',
    missingBill: 'Falta la factura',
    missingVehicleId: 'Falta el id del vehiculo',
    missingVehicleBrand: 'Falta la marca del vehiculo',
    missingVehicleModel: 'Falta el modelo del vehiculo',
    missingVehicleYear: 'Falta el año del vehiculo',
    missingVehicleColor: 'Falta el color del vehiculo',
    missingVehicleType: 'Falta el tipo de vehiculo',
    missingVehiclePrice: 'Falta el precio del vehiculo',
    missingVehicleStatus: 'Falta el status del vehiculo',
    missingVehiclePhoto: 'Falta la foto del vehiculo',
    missingVehicleBill: 'Falta la factura del vehiculo',
    missingVehiclePlate: 'Falta la placa del vehiculo',
    missingVehicleKilometers: 'Falta los kilometros del vehiculo',
    missingVehicleTransmission: 'Falta la transmision del vehiculo',
    missingVehicleDoors: 'Falta el numero de puertas del vehiculo',
    somethingWentWrong: 'Algo salio mal',
    users: {
      duplicateEmail: 'El email ya esta registrado',
      missingEmail: 'Falta el email',
      missingPassword: 'Falta la contraseña',
      missingId: 'Falta el id',
      missingName: 'Falta el nombre',
      missingLastName: 'Falta el apellido',
      requiredPass: 'Contraseña requerida',
      requiredEmail: 'Email requerido',
      notFound: 'Usuario no encontrado',
      wrongCredentials: 'Correo o contraseña incorrectos',
      codeNotFound: 'code query no encontrado',
      driverAccountFound: 'Conductor con cuenta CLABE asignada',
      driverAccountNotFound: 'Conductor sin cuenta CLABE asignada',
      googleRegisteredUserNotFound: 'Google_Registered_User_Not_Found',
      googleSignInfailureCountUpdateSuccess: 'Successfull updated googleSignfailureCount',
      googleSignInfailureCountUpdateFailure: 'Failed to update googleSignfailureCount',
    },
    tokens: {
      missingToken: 'Falta el token',
      invalidToken: 'Token invalido',
      googleIdTokenInvalid: 'Id_Token_Invalid',
    },
    email: {
      missingEmail: 'Falta el email',
      unauthorized: 'Usuario o Contraseña no autorizado',
      notFound: 'Email no encontrado',
    },
    driver: {
      addDriver: 'Error al agregar el conductor',
    },
    contract: {
      addContract: 'Error al agregar el contrato',
      findContractNumber: 'Error al obtener el número del contrato',
      contractNotFound: 'Contrato no encontrado',
    },
    vehicle: {
      vehicleNotFound: 'Vehiculo no encontrado',
    },
    payments: {
      paymentNotFound: 'Pago no encontrado',
      monexPaymentNotFound: 'Pago MONEX no encontrado',
    },
  },
  success: {
    users: {
      created: 'Usuario creado con exito',
      recoverPass: 'Email enviado correctamente',
      passChanged: 'Contraseña reestablecida con exito',
      loggin: 'Inicio de sesion exitoso',
      invitation: 'Invitación enviada satisfactoriamente',
    },
    driver: {
      addDriver: 'Conductor agregado con exito',
    },
    contract: {
      addContract: 'Contrato agregado con exito',
    },
  },
};

export const enumStepName = [
  'Stock',
  'Vehiculo listo',
  'Conductor asignado',
  'Reconocimiento del cliente',
  'Contrato generado',
  'Entregado',
  'Solicitud de reingreso',
];

export const enumStepNumber = [1, 2, 3, 4, 5, 6, 7, 8];

export type StepName =
  | 'Stock'
  | 'Vehiculo listo'
  | 'Conductor asignado'
  | 'Contrato generado'
  | 'Entregado'
  | 'Solicitud de reingreso';

export type StepNumber = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8;

export type StepProperties =
  | 'stock'
  | 'vehicleReady'
  | 'driverAssigned'
  | 'contractCreated'
  | 'delivered'
  | 'readmissions';

export const steps: Record<StepProperties, { name: StepName; number: StepNumber }> = {
  stock: {
    name: 'Stock',
    number: 1,
  },
  vehicleReady: {
    name: 'Vehiculo listo',
    number: 2,
  },
  driverAssigned: {
    name: 'Conductor asignado',
    number: 3,
  },
  contractCreated: {
    name: 'Contrato generado',
    number: 4,
  },
  delivered: {
    name: 'Entregado',
    number: 5,
  },
  readmissions: {
    name: 'Solicitud de reingreso',
    number: 6,
  },
};

export const wire4Data = {
  token: {
    scope: 'spei_admin device_local',
    type: 'AccesToken',
  },
  transfers: {
    response: {
      spei: {
        incomming: 'spei_incoming',
      },
    },
  },
  bankAccount: {
    currency_code: 'MXP',
  },
};

export const userRoles = {
  roles: {
    admin: 'administrador',
    superadmin: 'superadmin',
    associate: 'asociado',
    driver: 'conductor',
    backend: 'backend',
  },
  admin: ['administrador', 'superadmin'],
};

export const gpsText = {
  errors: {
    gpsNotFound: 'No se encontró el GPS en la base de datos',
  },
};

export const emailUsersAllowed = ['<EMAIL>', '<EMAIL>'];

export const citiesByRegion: { [key: string]: number } = {
  cdmx: 1,
  gdl: 2,
  mty: 3,
  qro: 4,
  tij: 5,
  moka: 6,
  pbe: 7,
  tol: 8,
  pbc: 7,
  ptv: 9,
  tep: 10,
  col: 11,
  sal: 12,
  torr: 13,
  dur: 14,
  mxli: 15,
  her: 16,
  chi: 17,
  leo: 18,
  ags: 19,
  slp: 20,
  mer: 21,
};

export const regionNumbertoCity: { [key: number]: string } = {
  1: 'cdmx',
  2: 'gdl',
  3: 'mty',
  4: 'qro',
  5: 'tij',
  6: 'moka',
  7: 'pbe',
  8: 'tol',
  9: 'ptv',
  10: 'tep',
  11: 'col',
  12: 'sal',
  13: 'torr',
  14: 'dur',
  15: 'mxli',
  16: 'her',
  17: 'chi',
  18: 'leo',
  19: 'ags',
  20: 'slp',
  21: 'mer',
};

export enum CountriesEnum {
  'United States' = 'United States',
  Mexico = 'Mexico',
}

export const Countries = [CountriesEnum.Mexico, CountriesEnum['United States']];
/* WEETRUST ENVS */

export const WEETRUST_URL = process.env.WEETRUST_URL!;
export const WEETRUST_USER_ID = process.env.WEETRUST_USER_ID!;
export const WEETRUST_API_KEY = process.env.WEETRUST_API_KEY!;
export const WEETRUST_ADD_ID = process.env.WEETRUST_ADD_ID;

export const DRIVER_WEB_APP_URL = process.env.DRIVER_WEB_APP_URL;

export const ONE_CAR_NOW_EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@onecarnow\.com$/;
export const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID!;
export const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET!;
export const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY!;
export const ENCRYPTION_IV = process.env.ENCRYPTION_IV!;
export const GEOCODING_API_KEY = process.env.GEOCODING_API_KEY!;
export const IPINFO_TOKEN = process.env.IPINFOTOKEN;

/**
 * doing this as per instructed by PM,
 * this is the list of emails that will be pre-selected for home visitors heads
 * as of now.
 */

const engineeringTeamEmails = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

export const preSelectedHomeVisitorsHeads = [
  ...engineeringTeamEmails,
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

export const homeVisitorsColors = [
  '#666E7D',
  '#FBB500',
  '#12BDB2',
  '#C3C7CE',
  '#666E7D',
  '#54A0CA',
  '#8239BC',
  '#1fe01f',
  '#1600ff',
  '#ff0001',
  '#29c9d6',
  '#a8cd32',
  '#e718d5',
  '#df7e20',
];

export const RUN_HOME_VISITORS_ADD_SLOTS_CRON = process.env.RUN_HOME_VISITORS_ADD_SLOTS_CRON === 'true';
export const RUN_HOME_VISIT_APPOINTMENTS_REMINDER_CRON =
  process.env.RUN_HOME_VISIT_APPOINTMENTS_REMINDER_CRON === 'true';
export const MAX_RESCHEDULE_ALLOWED = 2;
export const minBookingNoticeMinutes = 30;

export const cityOptions = [
  { key: 'CDMX/EDOMEX', label: 'Ciudad de México / Estado México' },
  { key: 'Guadalajara', label: 'Guadalajara' },
  { key: 'Monterrey', label: 'Monterrey' },
  { key: 'Puebla', label: 'Puebla' },
  { key: 'Tijuana', label: 'Tijuana' },
  { key: 'Queretaro', label: 'Queretaro' },
  { key: 'Cuernavaca', label: 'Cuernavaca' },
  { key: 'Otro', label: 'Otro' },
];

export const cityOptionsMap: Record<string, string> = {
  cdmx: 'CDMX/EDOMEX',
  edomx: 'CDMX/EDOMEX',
  gdl: 'Guadalajara',
  mty: 'Monterrey',
  pbe: 'Puebla',
  tij: 'Tijuana',
  qro: 'Queretaro',
  cuernavaca: 'Cuernavaca',
  otro: 'Otro',
};

export const cityOptions2Map: Record<string, string> = {
  // With this we verify that the incoming value exists in the options
  'CDMX/EDOMEX': 'CDMX/EDOMEX',
  Guadalajara: 'Guadalajara',
  Monterrey: 'Monterrey',
  Puebla: 'Puebla',
  Tijuana: 'Tijuana',
  Queretaro: 'Queretaro',
  Cuernavaca: 'Cuernavaca',
  Otro: 'Otro',
};

export enum Roles {
  superadmin = 'superadmin',
  administrador = 'administrador',
  agent = 'agent',
  auditor = 'auditor',
  lead = 'lead',
  Visitor = 'visitor',
}

export enum Areas {
  sales = 'sales',
  collection = 'collection',
  fleet = 'fleet',
  backoffice = 'backoffice',
  legal = 'legal',
  cx = 'cx',
  kyc = 'kyc',
  homeVisit = 'homeVisit',
  finance = 'finance',
  audit = 'audit',
  superadmin = 'superadmin',
}

export const RUN_FETCH_VEHICLE_VIOLATION_CRON = process.env.RUN_FETCH_VEHICLE_VIOLATION_CRON === 'true';
export const DAYS_THRESHOLD = 15; //fetch violation after x days
export const VIOLATION_LINK = 'https://data.finanzas.cdmx.gob.mx/consulta_adeudos';
export const CAPTCHA_LINK = 'https://data.finanzas.cdmx.gob.mx/captcha/flat';
export const requestDelaysInMinutes = [3, 5, 7];
export const VIOLATION_LINK_WITH_AMOUNT = 'https://infracciones.cdmx.gob.mx/llave/login/';
export const VIOLATION_LINK_WITH_AMOUNT_LOGIN_EMAIL = process.env.VIOLATION_LINK_WITH_AMOUNT_LOGIN_EMAIL!;
export const VIOLATION_LINK_WITH_AMOUNT_LOGIN_PASSWORD =
  process.env.VIOLATION_LINK_WITH_AMOUNT_LOGIN_PASSWORD!;
export const VIOLATION_LINK_WITH_AMOUNT_HOME_PAGE = 'https://infracciones.cdmx.gob.mx/';
export const VIOLATION_LINK_WITH_AMOUNT_PLATE_PAGE = 'https://infracciones.cdmx.gob.mx/otros/';

export const SpanishMonths: { [key: string]: string } = {
  enero: '01',
  febrero: '02',
  marzo: '03',
  abril: '04',
  mayo: '05',
  junio: '06',
  julio: '07',
  agosto: '08',
  septiembre: '09',
  octubre: '10',
  noviembre: '11',
  diciembre: '12',
};

export enum BlockLeadAssignationReason {
  Vacaciones = 'Vacaciones',
  BajaPorEnfermedad = 'Baja por enfermedad',
  Ausente = 'Ausente',
  PermisoDeEmergencia = 'Permiso de emergencia',
  AsignaciónEspecial = 'Asignación Especial',
  Rendimiento = 'Rendimiento',
  Terminación = 'Terminación',
}

export enum HandoverType {
  CUSTOMER = 'customer',
  AGENT = 'agent',
}
