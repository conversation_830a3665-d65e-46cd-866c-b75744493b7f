import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { GuestModel } from '../models/guest.model';
import { NotificationService } from '../services/notification.service';
import { OfficeUserModel } from '../models/user.model';
import mongoose from 'mongoose';

export class GuestController {
  private notificationService: NotificationService;

  constructor() {
    this.notificationService = new NotificationService();
  }

  // Create new guest
  async createGuest(req: Request, res: Response) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      const userId = req.user?.id;
      const guestData = {
        ...req.body,
        hostUserId: userId
      };

      const guest = new GuestModel(guestData);
      await guest.save();

      // Get host information for notifications
      const host = await OfficeUserModel.findById(userId);
      if (host) {
        // Send invitation notifications
        await this.notificationService.sendGuestInvitation(guest, host);
      }

      res.status(201).json({
        success: true,
        message: 'Guest created successfully',
        data: guest
      });
    } catch (error) {
      console.error('Error creating guest:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // Get guests for current user (as host)
  async getGuests(req: Request, res: Response) {
    try {
      const userId = req.user?.id;
      const {
        visitDate,
        status,
        page = 1,
        limit = 10,
        search
      } = req.query;

      const query: any = { hostUserId: userId };

      if (visitDate) {
        const date = new Date(visitDate as string);
        const nextDay = new Date(date);
        nextDay.setDate(date.getDate() + 1);
        query.visitDate = { $gte: date, $lt: nextDay };
      }

      if (status) {
        query.status = status;
      }

      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { company: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } }
        ];
      }

      const skip = (Number(page) - 1) * Number(limit);
      const guests = await GuestModel.find(query)
        .populate('hostUserId', 'firstName lastName email department')
        .sort({ visitDate: -1 })
        .skip(skip)
        .limit(Number(limit));

      const total = await GuestModel.countDocuments(query);

      res.json({
        success: true,
        data: guests,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      });
    } catch (error) {
      console.error('Error getting guests:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get specific guest
  async getGuestById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      const guest = await GuestModel.findOne({
        _id: id,
        hostUserId: userId
      }).populate('hostUserId', 'firstName lastName email department');

      if (!guest) {
        return res.status(404).json({
          success: false,
          message: 'Guest not found'
        });
      }

      res.json({
        success: true,
        data: guest
      });
    } catch (error) {
      console.error('Error getting guest:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Update guest
  async updateGuest(req: Request, res: Response) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors.array()
        });
      }

      const { id } = req.params;
      const userId = req.user?.id;

      const guest = await GuestModel.findOneAndUpdate(
        { _id: id, hostUserId: userId },
        req.body,
        { new: true, runValidators: true }
      ).populate('hostUserId', 'firstName lastName email department');

      if (!guest) {
        return res.status(404).json({
          success: false,
          message: 'Guest not found'
        });
      }

      res.json({
        success: true,
        message: 'Guest updated successfully',
        data: guest
      });
    } catch (error) {
      console.error('Error updating guest:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Confirm guest visit
  async confirmGuest(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      const guest = await GuestModel.findOneAndUpdate(
        { _id: id, hostUserId: userId },
        { status: 'confirmed' },
        { new: true }
      ).populate('hostUserId', 'firstName lastName email department');

      if (!guest) {
        return res.status(404).json({
          success: false,
          message: 'Guest not found'
        });
      }

      res.json({
        success: true,
        message: 'Guest visit confirmed',
        data: guest
      });
    } catch (error) {
      console.error('Error confirming guest:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Check-in guest
  async checkInGuest(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { identificationNumber, notes } = req.body;
      const receptionistId = req.user?.id;

      const guest = await GuestModel.findByIdAndUpdate(
        id,
        {
          status: 'checked-in',
          checkIn: {
            time: new Date(),
            by: receptionistId,
            notes
          },
          identificationNumber
        },
        { new: true }
      ).populate('hostUserId', 'firstName lastName email department slackUserId preferences');

      if (!guest) {
        return res.status(404).json({
          success: false,
          message: 'Guest not found'
        });
      }

      // Notify host of guest arrival
      if (guest.hostUserId) {
        await this.notificationService.notifyHostOfGuestArrival(guest, guest.hostUserId as any);
      }

      res.json({
        success: true,
        message: 'Guest checked in successfully',
        data: guest
      });
    } catch (error) {
      console.error('Error checking in guest:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Check-out guest
  async checkOutGuest(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { notes } = req.body;
      const receptionistId = req.user?.id;

      const guest = await GuestModel.findByIdAndUpdate(
        id,
        {
          status: 'checked-out',
          checkOut: {
            time: new Date(),
            by: receptionistId,
            notes
          }
        },
        { new: true }
      ).populate('hostUserId', 'firstName lastName email department');

      if (!guest) {
        return res.status(404).json({
          success: false,
          message: 'Guest not found'
        });
      }

      res.json({
        success: true,
        message: 'Guest checked out successfully',
        data: guest
      });
    } catch (error) {
      console.error('Error checking out guest:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Cancel guest visit
  async cancelGuest(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { reason } = req.body;
      const userId = req.user?.id;

      const guest = await GuestModel.findOneAndUpdate(
        { _id: id, hostUserId: userId },
        { 
          status: 'cancelled',
          cancellationReason: reason
        },
        { new: true }
      ).populate('hostUserId', 'firstName lastName email department');

      if (!guest) {
        return res.status(404).json({
          success: false,
          message: 'Guest not found'
        });
      }

      res.json({
        success: true,
        message: 'Guest visit cancelled',
        data: guest
      });
    } catch (error) {
      console.error('Error cancelling guest:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get today's guests (for reception)
  async getTodaysGuests(req: Request, res: Response) {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(today.getDate() + 1);

      const guests = await GuestModel.find({
        visitDate: { $gte: today, $lt: tomorrow },
        status: { $in: ['invited', 'confirmed', 'checked-in'] }
      })
      .populate('hostUserId', 'firstName lastName email department phone')
      .sort({ 'visitTime.start': 1 });

      res.json({
        success: true,
        data: guests
      });
    } catch (error) {
      console.error('Error getting today\'s guests:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Send guest invitation
  async sendInvitation(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { channels = ['email'] } = req.body;
      const userId = req.user?.id;

      const guest = await GuestModel.findOne({
        _id: id,
        hostUserId: userId
      }).populate('hostUserId');

      if (!guest) {
        return res.status(404).json({
          success: false,
          message: 'Guest not found'
        });
      }

      const results = await this.notificationService.sendGuestInvitation(guest, guest.hostUserId as any);

      res.json({
        success: true,
        message: 'Invitation sent',
        data: results
      });
    } catch (error) {
      console.error('Error sending invitation:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Delete guest
  async deleteGuest(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      const guest = await GuestModel.findOneAndDelete({
        _id: id,
        hostUserId: userId
      });

      if (!guest) {
        return res.status(404).json({
          success: false,
          message: 'Guest not found'
        });
      }

      res.json({
        success: true,
        message: 'Guest deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting guest:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
}
