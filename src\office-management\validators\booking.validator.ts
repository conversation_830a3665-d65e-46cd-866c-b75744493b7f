import { body, query, param } from 'express-validator';
import mongoose from 'mongoose';
import { MeetingRoomModel } from '../models/meeting-room.model';
import { BookingModel } from '../models/booking.model';

export class BookingValidator {
  static createBooking = [
    body('resourceType')
      .isIn(['meeting-room', 'desk'])
      .withMessage('Resource type must be either meeting-room or desk'),
    
    body('resourceId')
      .isMongoId()
      .withMessage('Resource ID must be a valid MongoDB ObjectId')
      .custom(async (value, { req }) => {
        if (req.body.resourceType === 'meeting-room') {
          const room = await MeetingRoomModel.findById(value);
          if (!room) {
            throw new Error('Meeting room not found');
          }
          if (!room.isActive) {
            throw new Error('Meeting room is not active');
          }
        }
        return true;
      }),
    
    body('title')
      .trim()
      .isLength({ min: 1, max: 200 })
      .withMessage('Title must be between 1 and 200 characters'),
    
    body('description')
      .optional()
      .trim()
      .isLength({ max: 1000 })
      .withMessage('Description must not exceed 1000 characters'),
    
    body('startDate')
      .isISO8601()
      .withMessage('Start date must be a valid ISO 8601 date')
      .custom((value) => {
        const startDate = new Date(value);
        const now = new Date();
        if (startDate <= now) {
          throw new Error('Start date must be in the future');
        }
        return true;
      }),
    
    body('endDate')
      .isISO8601()
      .withMessage('End date must be a valid ISO 8601 date')
      .custom((value, { req }) => {
        const startDate = new Date(req.body.startDate);
        const endDate = new Date(value);
        if (endDate <= startDate) {
          throw new Error('End date must be after start date');
        }
        return true;
      }),
    
    body('attendees.internal')
      .optional()
      .isArray()
      .withMessage('Internal attendees must be an array')
      .custom((value) => {
        if (value && value.length > 0) {
          const invalidIds = value.filter((id: string) => !mongoose.Types.ObjectId.isValid(id));
          if (invalidIds.length > 0) {
            throw new Error('All internal attendee IDs must be valid MongoDB ObjectIds');
          }
        }
        return true;
      }),
    
    body('attendees.external')
      .optional()
      .isArray()
      .withMessage('External attendees must be an array')
      .custom((value) => {
        if (value && value.length > 0) {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          const invalidEmails = value.filter((email: string) => !emailRegex.test(email));
          if (invalidEmails.length > 0) {
            throw new Error('All external attendee emails must be valid');
          }
        }
        return true;
      }),
    
    body('guests')
      .optional()
      .isArray()
      .withMessage('Guests must be an array')
      .custom((value) => {
        if (value && value.length > 0) {
          const invalidIds = value.filter((id: string) => !mongoose.Types.ObjectId.isValid(id));
          if (invalidIds.length > 0) {
            throw new Error('All guest IDs must be valid MongoDB ObjectIds');
          }
        }
        return true;
      })
  ];

  static updateBooking = [
    param('id')
      .isMongoId()
      .withMessage('Booking ID must be a valid MongoDB ObjectId'),
    
    body('title')
      .optional()
      .trim()
      .isLength({ min: 1, max: 200 })
      .withMessage('Title must be between 1 and 200 characters'),
    
    body('description')
      .optional()
      .trim()
      .isLength({ max: 1000 })
      .withMessage('Description must not exceed 1000 characters'),
    
    body('startDate')
      .optional()
      .isISO8601()
      .withMessage('Start date must be a valid ISO 8601 date')
      .custom(async (value, { req }) => {
        if (value) {
          const startDate = new Date(value);
          const now = new Date();
          
          // Check if booking exists and get current start date
          const booking = await BookingModel.findById(req.params.id);
          if (!booking) {
            throw new Error('Booking not found');
          }
          
          // Allow updating if the booking hasn't started yet
          if (booking.startDate <= now && startDate !== booking.startDate.toISOString()) {
            throw new Error('Cannot modify start date of a booking that has already started');
          }
          
          if (startDate <= now) {
            throw new Error('Start date must be in the future');
          }
        }
        return true;
      }),
    
    body('endDate')
      .optional()
      .isISO8601()
      .withMessage('End date must be a valid ISO 8601 date')
      .custom((value, { req }) => {
        if (value) {
          const startDate = req.body.startDate ? new Date(req.body.startDate) : null;
          const endDate = new Date(value);
          
          if (startDate && endDate <= startDate) {
            throw new Error('End date must be after start date');
          }
        }
        return true;
      })
  ];

  static cancelBooking = [
    param('id')
      .isMongoId()
      .withMessage('Booking ID must be a valid MongoDB ObjectId'),
    
    body('cancellationReason')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Cancellation reason must not exceed 500 characters')
  ];

  static getBookings = [
    query('startDate')
      .optional()
      .isISO8601()
      .withMessage('Start date must be a valid ISO 8601 date'),
    
    query('endDate')
      .optional()
      .isISO8601()
      .withMessage('End date must be a valid ISO 8601 date'),
    
    query('status')
      .optional()
      .isIn(['confirmed', 'cancelled', 'completed', 'no-show'])
      .withMessage('Status must be one of: confirmed, cancelled, completed, no-show'),
    
    query('resourceType')
      .optional()
      .isIn(['meeting-room', 'desk'])
      .withMessage('Resource type must be either meeting-room or desk'),
    
    query('resourceId')
      .optional()
      .isMongoId()
      .withMessage('Resource ID must be a valid MongoDB ObjectId'),
    
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
  ];

  static checkConflicts = [
    query('resourceType')
      .isIn(['meeting-room', 'desk'])
      .withMessage('Resource type must be either meeting-room or desk'),
    
    query('resourceId')
      .isMongoId()
      .withMessage('Resource ID must be a valid MongoDB ObjectId'),
    
    query('startDate')
      .isISO8601()
      .withMessage('Start date must be a valid ISO 8601 date'),
    
    query('endDate')
      .isISO8601()
      .withMessage('End date must be a valid ISO 8601 date')
      .custom((value, { req }) => {
        const startDate = new Date(req.query.startDate as string);
        const endDate = new Date(value);
        if (endDate <= startDate) {
          throw new Error('End date must be after start date');
        }
        return true;
      }),
    
    query('excludeBookingId')
      .optional()
      .isMongoId()
      .withMessage('Exclude booking ID must be a valid MongoDB ObjectId')
  ];

  static bulkCreateBookings = [
    body('resourceType')
      .isIn(['meeting-room', 'desk'])
      .withMessage('Resource type must be either meeting-room or desk'),
    
    body('resourceId')
      .isMongoId()
      .withMessage('Resource ID must be a valid MongoDB ObjectId'),
    
    body('title')
      .trim()
      .isLength({ min: 1, max: 200 })
      .withMessage('Title must be between 1 and 200 characters'),
    
    body('startTime')
      .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .withMessage('Start time must be in HH:MM format'),
    
    body('endTime')
      .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .withMessage('End time must be in HH:MM format')
      .custom((value, { req }) => {
        const startTime = req.body.startTime;
        const endTime = value;
        
        const [startHour, startMin] = startTime.split(':').map(Number);
        const [endHour, endMin] = endTime.split(':').map(Number);
        
        const startMinutes = startHour * 60 + startMin;
        const endMinutes = endHour * 60 + endMin;
        
        if (endMinutes <= startMinutes) {
          throw new Error('End time must be after start time');
        }
        
        return true;
      }),
    
    body('recurrence.type')
      .isIn(['daily', 'weekly', 'monthly'])
      .withMessage('Recurrence type must be daily, weekly, or monthly'),
    
    body('recurrence.startDate')
      .isISO8601()
      .withMessage('Recurrence start date must be a valid ISO 8601 date'),
    
    body('recurrence.endDate')
      .isISO8601()
      .withMessage('Recurrence end date must be a valid ISO 8601 date')
      .custom((value, { req }) => {
        const startDate = new Date(req.body.recurrence.startDate);
        const endDate = new Date(value);
        if (endDate <= startDate) {
          throw new Error('Recurrence end date must be after start date');
        }
        return true;
      }),
    
    body('recurrence.daysOfWeek')
      .optional()
      .isArray()
      .withMessage('Days of week must be an array')
      .custom((value) => {
        if (value && value.length > 0) {
          const validDays = value.every((day: number) => Number.isInteger(day) && day >= 0 && day <= 6);
          if (!validDays) {
            throw new Error('Days of week must be integers between 0 (Sunday) and 6 (Saturday)');
          }
        }
        return true;
      })
  ];

  static getBookingById = [
    param('id')
      .isMongoId()
      .withMessage('Booking ID must be a valid MongoDB ObjectId')
  ];

  static checkInOut = [
    param('id')
      .isMongoId()
      .withMessage('Booking ID must be a valid MongoDB ObjectId')
  ];
}
