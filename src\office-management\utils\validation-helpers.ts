import { GOOGLE_SSO_CONFIG, ADVANCE_BOOKING_DAYS, BOOKING_DURATION_HOURS } from '../constants';
import { DateHelpers } from './date-helpers';
import { HttpException } from '../exceptions/HttpExceptions';
import mongoose from 'mongoose';

export class ValidationHelpers {
  /**
   * Validate email domain
   */
  static validateEmailDomain(email: string): boolean {
    return email.endsWith(`@${GOOGLE_SSO_CONFIG.ALLOWED_DOMAIN}`);
  }

  /**
   * Validate booking date
   */
  static validateBookingDate(date: Date): void {
    if (DateHelpers.isPastDate(date)) {
      throw HttpException.BadRequest('Cannot book for past dates');
    }

    if (DateHelpers.isTooFarInFuture(date, ADVANCE_BOOKING_DAYS.MAX)) {
      throw HttpException.BadRequest(`Cannot book more than ${ADVANCE_BOOKING_DAYS.MAX} days in advance`);
    }

    if (DateHelpers.isWeekend(date)) {
      throw HttpException.BadRequest('Bookings are not allowed on weekends');
    }
  }

  /**
   * Validate booking time
   */
  static validateBookingTime(time: string): void {
    if (!DateHelpers.isWithinBusinessHours(time)) {
      throw HttpException.BadRequest('Bookings are only allowed during business hours');
    }
  }

  /**
   * Validate booking duration
   */
  static validateBookingDuration(durationHours: number): void {
    if (durationHours < BOOKING_DURATION_HOURS.MIN || durationHours > BOOKING_DURATION_HOURS.MAX) {
      throw HttpException.BadRequest(
        `Booking duration must be between ${BOOKING_DURATION_HOURS.MIN} and ${BOOKING_DURATION_HOURS.MAX} hours`
      );
    }
  }

  /**
   * Validate phone number format
   */
  static validatePhoneNumber(phone: string): boolean {
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phone);
  }

  /**
   * Validate email format
   */
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Sanitize string input
   */
  static sanitizeString(input: string): string {
    return input.trim().replace(/[<>]/g, '');
  }

  /**
   * Validate required fields
   */
  static validateRequiredFields(data: Record<string, any>, requiredFields: string[]): void {
    const missingFields = requiredFields.filter(field => !data[field]);

    if (missingFields.length > 0) {
      throw HttpException.BadRequest(`Missing required fields: ${missingFields.join(', ')}`);
    }
  }

  /**
   * Validate meeting room capacity
   */
  static validateMeetingRoomCapacity(capacity: number): void {
    if (capacity < 1 || capacity > 100) {
      throw HttpException.BadRequest('Meeting room capacity must be between 1 and 100');
    }
  }

  /**
   * Validate desk location
   */
  static validateDeskLocation(location: string): void {
    if (!location || location.length < 2) {
      throw HttpException.BadRequest('Desk location must be at least 2 characters long');
    }
  }

  /**
   * Validate MongoDB ObjectId
   */
  static isValidObjectId(id: string): boolean {
    return mongoose.Types.ObjectId.isValid(id);
  }
}
