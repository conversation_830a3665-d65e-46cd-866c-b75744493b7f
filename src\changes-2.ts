import xlsx from 'xlsx';
import fs from 'fs';
import path from 'path';
import { DateTime } from 'luxon';
import TempSuscriptionPayments from './modules/TempSuscriptionPayments/model/tempSuscriptionPayment.model';

/**
 * Compara los contratos del pending.xlsx con los archivos PDF generados en la carpeta pagares
 * y muestra cuáles faltan.
 */
// getMissingPagareFiles();
export async function getMissingPagareFiles() {
  const root = process.cwd();
  const filePath = path.join(root, 'pending.xlsx');
  const pagaresFolderPath = path.join(root, 'pagares');

  // Leer contratos del Excel
  const workbook = xlsx.readFile(filePath);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  const data = xlsx.utils.sheet_to_json(worksheet, { raw: false });

  // Obtener todos los contractNumbers del Excel
  const carNumbersToUpdate = data.map(
    (item: any) => item.Contrato.toString().replace(',', '').replace('.', '-').split('-')[0]
  );

  // Leer archivos en la carpeta pagares
  const files = fs.readdirSync(pagaresFolderPath);

  // Extraer contractNumbers de los nombres de archivo
  const contractNumbersInFolder = files
    .map((filename) => {
      // El nombre esperado es: contractNumber-...pdf
      const match = filename.match(/^([^-]+)-/);
      return match ? match[1] : null;
    })
    .filter(Boolean);

  // Filtrar los contractNumbers que no tienen archivo PDF
  const missing = carNumbersToUpdate.filter((carNumber) => !contractNumbersInFolder.includes(carNumber));

  console.log('Total en Excel:', carNumbersToUpdate.length);
  console.log('Total archivos PDF:', contractNumbersInFolder.length);
  console.log('Faltan:', missing.length);
  console.log('Contratos faltantes:', missing);

  // Opcional: guardar en un archivo
  fs.writeFileSync(path.join(root, 'faltantes-pagares.json'), JSON.stringify(missing, null, 2));
}

export const normalizeTempSuscriptionDates = async () => {
  try {
    // Obtener todos los registros activos y pendientes
    const tempPayments = await TempSuscriptionPayments.find({
      // status: { $in: ['active', 'pending', 'inactive'] },
      // dateToAddTempProducts: { $exists: true, $ne: null },
    });
    // .limit(1000);

    console.log(`Total registros a normalizar: ${tempPayments.length}`);
    // return;
    let updatedCount = 0;

    for (const payment of tempPayments) {
      let updated = false;
      console.log('payment', payment);
      // copy the dateToAddTempProducts instead of having reference to same object
      const copyDateToAddTempProducts = payment.dateToAddTempProducts
        ? new Date(payment.dateToAddTempProducts)
        : null;
      // get the name of the day like Monday, Tuesday, etc

      // Normalizar dateToAddTempProducts (convertir de jueves a lunes siguiente)
      if (payment.dateToAddTempProducts) {
        const getNameDate = copyDateToAddTempProducts?.toLocaleDateString('en-US', { weekday: 'long' });
        console.log(
          'payment.dateToAddTempProducts',
          payment.dateToAddTempProducts?.toISOString(),
          getNameDate
        );
        console.log('-----------------------------------------------------------');
        const date = payment.dateToAddTempProducts;
        const dateTime = DateTime.fromJSDate(date);
        const dayOfWeek = dateTime.weekday; // En Luxon: 1 = lunes, 4 = jueves, 7 = domingo

        if (dayOfWeek !== 1) {
          // Si no es lunes
          // Calculamos el siguiente lunes
          // Si es jueves (4), sumamos 4 días para llegar al lunes siguiente
          // Para otros días, calculamos cuántos días faltan hasta el lunes
          let nextMonday;

          if (dayOfWeek === 4) {
            // Jueves
            nextMonday = dateTime.plus({ days: 4 });
          } else if (dayOfWeek < 1) {
            // Domingo (7)
            nextMonday = dateTime.plus({ days: 1 });
          } else {
            // Otros días
            nextMonday = dateTime.plus({ days: 8 - dayOfWeek });
          }

          payment.dateToAddTempProducts = nextMonday.toJSDate();
          updated = true;
          console.log(
            `Registro ${payment._id}: dateToAddTempProducts actualizado de ${dateTime.toISO()} a ${nextMonday.toISO()}`
          );
          console.log('-----------------------------------------------------------');
        }
      }

      // Normalizar stopDate si existe
      if (payment.stopDate) {
        const date = payment.stopDate;
        const dateTime = DateTime.fromJSDate(date);
        const dayOfWeek = dateTime.weekday;

        if (dayOfWeek !== 1) {
          let nextMonday;

          if (dayOfWeek < 1) {
            // Domingo (7)
            nextMonday = dateTime.plus({ days: 1 });
          } else {
            // Otros días
            nextMonday = dateTime.plus({ days: 8 - dayOfWeek });
          }

          payment.stopDate = nextMonday.toJSDate();
          updated = true;
          console.log(
            `Registro ${payment._id}: stopDate actualizado de ${dateTime.toISO()} a ${nextMonday.toISO()}`
          );
          console.log('-----------------------------------------------------------');
        }
      }

      // Normalizar activationDate si existe
      if (payment.activationDate) {
        const date = payment.activationDate;
        const dateTime = DateTime.fromJSDate(date);
        const dayOfWeek = dateTime.weekday;

        if (dayOfWeek !== 1) {
          let nextMonday;

          if (dayOfWeek < 1) {
            // Domingo (7)
            nextMonday = dateTime.plus({ days: 1 });
          } else {
            // Otros días
            nextMonday = dateTime.plus({ days: 8 - dayOfWeek });
          }

          payment.activationDate = nextMonday.toJSDate();
          updated = true;
          console.log(
            `Registro ${payment._id}: activationDate actualizado de ${dateTime.toISO()} a ${nextMonday.toISO()}`
          );
          console.log('-----------------------------------------------------------');
        }
      }

      // Normalizar expirationDate de cada item
      // if (payment.tempItems && payment.tempItems.length > 0) {
      //   for (let i = 0; i < payment.tempItems.length; i++) {
      //     const item = payment.tempItems[i];
      //     if (item.expirationDate) {
      //       const date = item.expirationDate;
      //       const dateTime = DateTime.fromJSDate(date);
      //       const dayOfWeek = dateTime.weekday;

      //       if (dayOfWeek !== 1) {
      //         let nextMonday;

      //         if (dayOfWeek < 1) {
      //           // Domingo (7)
      //           nextMonday = dateTime.plus({ days: 1 });
      //         } else {
      //           // Otros días
      //           nextMonday = dateTime.plus({ days: 8 - dayOfWeek });
      //         }

      //         payment.tempItems[i].expirationDate = nextMonday.toJSDate();
      //         updated = true;
      //         console.log(
      //           `Registro ${payment._id}, Item ${i}: expirationDate actualizado de ${dateTime.toISO()} a ${nextMonday.toISO()}`
      //         );
      //         console.log('-----------------------------------------------------------');
      //       }
      //     }
      //   }
      // }

      if (updated) {
        // await payment.save();
        console.log(
          `Registro ${payment._id} actualizado, antes con la fecha ${copyDateToAddTempProducts ? copyDateToAddTempProducts.toISOString() : 'No date'} y ahora con la fecha ${payment.dateToAddTempProducts?.toISOString() || 'No date'}`
        );
        updatedCount++;
      }
      console.log('===========================================================');
    }

    console.log(`Normalización completada. ${updatedCount} registros actualizados.`);
  } catch (error) {
    console.error('Error al normalizar fechas:', error);
  }
};
